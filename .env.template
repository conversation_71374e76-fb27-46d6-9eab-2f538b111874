# AI Provider API Keys
OPENAI_API_KEY=your_openai_key_here
ANTHROPIC_API_KEY=your_anthropic_key_here
GOOGLE_API_KEY=your_google_key_here
DEEPSEEK_API_KEY=your_deepseek_key_here
OPENROUTER_API_KEY=your_openrouter_key_here
MISTRAL_API_KEY=your_mistral_key_here

# Ollama Configuration
OLLAMA_BASE_URL=http://localhost:11434
OLLAMA_MODEL=llama3.2-vision

# Custom LLM Configuration
CUSTOM_LLM_ENDPOINT=
CUSTOM_LLM_API_KEY=
CUSTOM_LLM_MODEL=

# Application Settings
DEFAULT_AI_PROVIDER=deepseek
FALLBACK_PROVIDERS=openai,anthropic,google
MAX_FILE_SIZE_MB=10
SUPPORTED_LANGUAGES=en,es,fr,de,ar,zh,ja
LOG_LEVEL=INFO
MAX_RETRIES=3
RETRY_DELAY=2

# Processing Settings
BATCH_SIZE=5
PARALLEL_PROCESSING=true
CONFIDENCE_THRESHOLD=0.8

# Output Settings
OUTPUT_FORMAT=xlsx
DATE_FORMAT=DD/MM/YYYY
CURRENCY_FORMAT=local

# File Organization
AUTO_ARCHIVE=true
KEEP_ORIGINALS=true
DUPLICATE_HANDLING=skip
