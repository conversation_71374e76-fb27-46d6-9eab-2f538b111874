#!/usr/bin/env python3
"""
Test script to verify currency conversion functionality.
"""

import sys
from pathlib import Path

# Add src to path
sys.path.append(str(Path(__file__).parent / "src"))

from src.utils import CurrencyProcessor
from config.settings import settings

def test_currency_conversion():
    """Test currency conversion functionality."""
    print("Testing Currency Conversion")
    print("=" * 40)
    
    # Test data similar to your invoice data
    test_cases = [
        {"amount": 10.00, "from": "USD", "to": "AED", "expected_approx": 36.7},
        {"amount": 307.62, "from": "AED", "to": "AED", "expected_approx": 307.62},  # Same currency
        {"amount": 100.00, "from": "USD", "to": "AED", "expected_approx": 367.0},
    ]
    
    print(f"Base currency setting: {settings.base_currency}")
    print()
    
    for i, case in enumerate(test_cases, 1):
        converted = CurrencyProcessor.convert_currency(
            case["amount"], 
            case["from"], 
            case["to"]
        )
        
        print(f"Test {i}:")
        print(f"  {case['amount']} {case['from']} → {converted:.2f} {case['to']}")
        print(f"  Expected ~{case['expected_approx']:.2f}")
        print(f"  ✓ Conversion successful")
        print()

def test_summary_calculation():
    """Test summary calculation with mixed currencies."""
    print("Testing Summary Calculation with Mixed Currencies")
    print("=" * 50)
    
    # Sample invoice data like yours
    sample_invoices = [
        {"total_amount": 10.00, "currency": "USD", "date": "2025-06-01"},  # June USD
        {"total_amount": 307.62, "currency": "AED", "date": "2025-07-01"},  # July AED
    ]
    
    total_in_base = 0.0
    base_currency = settings.base_currency
    
    print(f"Converting all amounts to base currency: {base_currency}")
    print()
    
    for invoice in sample_invoices:
        amount = invoice["total_amount"]
        currency = invoice["currency"]
        
        converted = CurrencyProcessor.convert_currency(amount, currency, base_currency)
        total_in_base += converted
        
        print(f"Invoice: {amount} {currency} → {converted:.2f} {base_currency}")
    
    print(f"\nTotal in {base_currency}: {total_in_base:.2f}")
    print(f"This should match your corrected summary total!")

if __name__ == "__main__":
    test_currency_conversion()
    print()
    test_summary_calculation()
