# Setup Guide 🛠️

This guide will walk you through setting up the Invoice Processor application step by step.

## System Requirements

### Minimum Requirements
- **Operating System**: Windows 10/11, macOS 10.14+, or Linux (Ubuntu 18.04+)
- **Python**: 3.8 or higher
- **RAM**: 4GB minimum, 8GB recommended
- **Storage**: 2GB free space
- **Internet**: Required for AI provider APIs

### Recommended Requirements
- **Python**: 3.10 or higher
- **RAM**: 16GB for large batch processing
- **Storage**: 10GB for processing archives
- **CPU**: Multi-core processor for parallel processing

## Step 1: Python Installation

### Windows
1. Download Python from https://python.org/downloads/
2. Run installer and check "Add Python to PATH"
3. Verify installation:
```cmd
python --version
pip --version
```

### macOS
```bash
# Using Homebrew (recommended)
brew install python

# Or download from python.org
```

### Linux (Ubuntu/Debian)
```bash
sudo apt update
sudo apt install python3 python3-pip python3-venv
```

## Step 2: Install Tesseract OCR

### Windows
1. Download from: https://github.com/UB-Mannheim/tesseract/wiki
2. Install to default location
3. Add to PATH or note installation directory

### macOS
```bash
brew install tesseract
```

### Linux
```bash
sudo apt install tesseract-ocr
```

## Step 3: Project Setup

### 1. Download/Clone Project
```bash
# If using git
git clone <repository-url>
cd invoice-processor

# Or download and extract ZIP file
```

### 2. Create Virtual Environment (Recommended)
```bash
# Create virtual environment
python -m venv invoice_env

# Activate virtual environment
# Windows:
invoice_env\Scripts\activate
# macOS/Linux:
source invoice_env/bin/activate
```

### 3. Install Dependencies
```bash
pip install -r requirements.txt
```

### 4. Create Environment Configuration
```bash
# Copy template
cp .env.template .env

# Edit .env file with your settings
```

## Step 4: AI Provider Setup

### DeepSeek (Recommended Primary)

**Why DeepSeek?**
- Cost-effective ($0.0002 per 1K tokens)
- Good accuracy for invoice processing
- Fast response times

**Setup:**
1. Visit https://platform.deepseek.com/
2. Create account and verify email
3. Navigate to API Keys section
4. Create new API key
5. Add to .env file:
```bash
DEEPSEEK_API_KEY=your_deepseek_key_here
DEFAULT_AI_PROVIDER=deepseek
```

### OpenAI (Fallback)

**Setup:**
1. Visit https://platform.openai.com/
2. Create account and add payment method
3. Generate API key
4. Add to .env file:
```bash
OPENAI_API_KEY=your_openai_key_here
```

### Anthropic Claude (Fallback)

**Setup:**
1. Visit https://console.anthropic.com/
2. Create account and verify
3. Generate API key
4. Add to .env file:
```bash
ANTHROPIC_API_KEY=your_anthropic_key_here
```

### Google Gemini (Fallback)

**Setup:**
1. Visit https://makersuite.google.com/
2. Create/use Google account
3. Generate API key
4. Add to .env file:
```bash
GOOGLE_API_KEY=your_google_key_here
```

## Step 5: Configuration

### Basic Configuration (.env)
```bash
# AI Providers
DEFAULT_AI_PROVIDER=deepseek
FALLBACK_PROVIDERS=openai,anthropic,google
DEEPSEEK_API_KEY=your_deepseek_key_here
OPENAI_API_KEY=your_openai_key_here
ANTHROPIC_API_KEY=your_anthropic_key_here
GOOGLE_API_KEY=your_google_key_here

# Processing Settings
MAX_FILE_SIZE_MB=10
BATCH_SIZE=5
CONFIDENCE_THRESHOLD=0.8
LOG_LEVEL=INFO

# File Organization
AUTO_ARCHIVE=true
KEEP_ORIGINALS=true
DUPLICATE_HANDLING=skip
```

### Advanced Configuration

**For High Volume Processing:**
```bash
BATCH_SIZE=10
PARALLEL_PROCESSING=true
MAX_RETRIES=5
RETRY_DELAY=3
```

**For Privacy-Focused Processing:**
```bash
DEFAULT_AI_PROVIDER=ollama
OLLAMA_BASE_URL=http://localhost:11434
OLLAMA_MODEL=llama3.2-vision
```

## Step 6: Initial Testing

### 1. System Health Check
```bash
python main.py health
```

Expected output:
- ✅ Disk space sufficient
- ✅ Memory available
- ✅ Directories accessible

### 2. Test AI Connections
```bash
python main.py test-connections
```

Expected output:
- ✅ DeepSeek connected
- ✅ Other providers (if configured)

### 3. Test with Sample Invoice
1. Place a sample invoice image in `data/invoices/`
2. Run processing:
```bash
python main.py process
```

## Step 7: Directory Structure Setup

The application will automatically create these directories:

```
invoice_processor/
├── data/
│   ├── invoices/          # Place your invoice images here
│   ├── processed/         # Archived processed files
│   └── output/           # Generated Excel files
└── logs/                 # Application logs
```

## Troubleshooting Setup Issues

### Python Issues
**"Python not found"**
- Ensure Python is in PATH
- Try `python3` instead of `python`
- Reinstall Python with "Add to PATH" option

**"pip not found"**
- Try `python -m pip` instead of `pip`
- Reinstall Python or install pip separately

### Dependency Issues
**"Package installation failed"**
```bash
# Upgrade pip first
pip install --upgrade pip

# Install with verbose output
pip install -r requirements.txt -v

# Try installing packages individually
pip install openai anthropic google-generativeai
```

### Tesseract Issues
**"Tesseract not found"**
- Verify installation: `tesseract --version`
- Add to PATH or specify location in code
- Reinstall Tesseract

### API Key Issues
**"API key not configured"**
- Check .env file exists and has correct keys
- Verify no extra spaces or quotes around keys
- Test keys with provider's documentation

**"API authentication failed"**
- Verify API key is correct and active
- Check account has sufficient credits
- Ensure API key has correct permissions

### Permission Issues
**"Permission denied"**
```bash
# Linux/macOS: Fix permissions
chmod +x main.py
sudo chown -R $USER:$USER invoice_processor/

# Windows: Run as administrator if needed
```

## Performance Optimization

### For Large Batches
```bash
# Increase batch size
BATCH_SIZE=15

# Enable parallel processing
PARALLEL_PROCESSING=true

# Optimize memory usage
MAX_FILE_SIZE_MB=5
```

### For Better Accuracy
```bash
# Lower confidence threshold
CONFIDENCE_THRESHOLD=0.7

# Enable image preprocessing
# (enabled by default in CLI)
```

### For Cost Optimization
```bash
# Use cost-effective provider
DEFAULT_AI_PROVIDER=deepseek

# Reduce retries
MAX_RETRIES=2

# Skip preprocessing for clear images
# Use --no-preprocess flag
```

## Next Steps

1. **Test with Sample Data**: Process a few sample invoices
2. **Configure Automation**: Set up batch processing schedules
3. **Monitor Usage**: Check logs and API usage
4. **Optimize Settings**: Adjust based on your specific needs
5. **Backup Configuration**: Save your .env file securely

## Getting Help

If you encounter issues:

1. **Check Logs**: Look in `logs/` directory
2. **Run Health Check**: `python main.py health`
3. **Test Connections**: `python main.py test-connections`
4. **Review Documentation**: Check README.md
5. **Check Requirements**: Verify all dependencies installed

## Security Best Practices

1. **Protect API Keys**: Never commit .env file to version control
2. **Regular Rotation**: Rotate API keys periodically
3. **Monitor Usage**: Track API usage and costs
4. **Secure Logs**: Protect log files containing sensitive data
5. **Local Processing**: Use Ollama for sensitive documents

---

**Setup Complete! 🎉**

You're now ready to start processing invoices. Begin with:
```bash
python main.py process
```
