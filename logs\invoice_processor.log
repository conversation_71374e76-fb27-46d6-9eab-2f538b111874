2025-07-12 21:54:47 | INFO     | src.logging_config:setup_logging:73 - Logging system initialized
2025-07-12 21:55:04 | INFO     | src.logging_config:setup_logging:73 - Logging system initialized
2025-07-12 21:55:16 | INFO     | src.logging_config:setup_logging:73 - Logging system initialized
2025-07-12 22:10:11 | INFO     | src.logging_config:setup_logging:73 - Logging system initialized
2025-07-12 22:10:11 | INFO     | src.ai_manager:__init__:35 - AI Manager initialized with primary: deepseek
2025-07-12 22:10:11 | INFO     | src.ai_manager:__init__:36 - Available providers: ['deepseek', 'openrouter', 'ollama']
2025-07-12 22:10:11 | INFO     | src.invoice_processor:__init__:78 - Invoice Processor initialized
2025-07-12 22:10:11 | INFO     | src.excel_manager:__init__:40 - Excel Manager initialized
2025-07-12 22:10:12 | INFO     | src.ai_manager:__init__:35 - AI Manager initialized with primary: deepseek
2025-07-12 22:10:12 | INFO     | src.ai_manager:__init__:36 - Available providers: ['deepseek', 'openrouter', 'ollama']
2025-07-12 22:10:31 | INFO     | src.logging_config:setup_logging:73 - Logging system initialized
2025-07-12 22:11:25 | INFO     | src.logging_config:setup_logging:73 - Logging system initialized
2025-07-12 22:11:26 | INFO     | src.ai_manager:__init__:35 - AI Manager initialized with primary: deepseek
2025-07-12 22:11:26 | INFO     | src.ai_manager:__init__:36 - Available providers: ['deepseek', 'openrouter', 'ollama']
2025-07-12 22:11:26 | INFO     | src.invoice_processor:__init__:78 - Invoice Processor initialized
2025-07-12 22:11:26 | INFO     | src.excel_manager:__init__:40 - Excel Manager initialized
2025-07-12 22:11:26 | INFO     | src.ai_manager:__init__:35 - AI Manager initialized with primary: deepseek
2025-07-12 22:11:26 | INFO     | src.ai_manager:__init__:36 - Available providers: ['deepseek', 'openrouter', 'ollama']
2025-07-12 22:11:26 | INFO     | src.invoice_processor:discover_invoice_files:275 - Discovered 4 invoice files in G:\vscodeapps\augment\invoicerecorder\data\invoices
2025-07-12 22:11:33 | INFO     | src.invoice_processor:process_batch:218 - Starting batch processing of 4 files
2025-07-12 22:11:33 | INFO     | src.invoice_processor:process_single_invoice:100 - Processing invoice: G:\vscodeapps\augment\invoicerecorder\data\invoices\Invoice_ (1).jpg
2025-07-12 22:11:34 | DEBUG    | src.utils:enhance_image_pil:104 - Enhanced image saved to: G:\vscodeapps\augment\invoicerecorder\data\processed\enhanced_Invoice_ (1).jpg
2025-07-12 22:11:34 | INFO     | src.ai_manager:process_invoice:63 - Attempting to process with provider: deepseek
2025-07-12 22:11:34 | INFO     | src.invoice_processor:process_single_invoice:100 - Processing invoice: G:\vscodeapps\augment\invoicerecorder\data\invoices\Invoice_ (2).jpg
2025-07-12 22:11:35 | DEBUG    | src.utils:enhance_image_pil:104 - Enhanced image saved to: G:\vscodeapps\augment\invoicerecorder\data\processed\enhanced_Invoice_ (2).jpg
2025-07-12 22:11:35 | INFO     | src.ai_manager:process_invoice:63 - Attempting to process with provider: deepseek
2025-07-12 22:11:35 | INFO     | src.invoice_processor:process_single_invoice:100 - Processing invoice: G:\vscodeapps\augment\invoicerecorder\data\invoices\Invoice_ (3).jpg
2025-07-12 22:11:36 | DEBUG    | src.utils:enhance_image_pil:104 - Enhanced image saved to: G:\vscodeapps\augment\invoicerecorder\data\processed\enhanced_Invoice_ (3).jpg
2025-07-12 22:11:36 | INFO     | src.ai_manager:process_invoice:63 - Attempting to process with provider: deepseek
2025-07-12 22:11:36 | INFO     | src.invoice_processor:process_single_invoice:100 - Processing invoice: G:\vscodeapps\augment\invoicerecorder\data\invoices\Invoice_ (4).jpg
2025-07-12 22:11:37 | DEBUG    | src.utils:enhance_image_pil:104 - Enhanced image saved to: G:\vscodeapps\augment\invoicerecorder\data\processed\enhanced_Invoice_ (4).jpg
2025-07-12 22:11:37 | INFO     | src.ai_manager:process_invoice:63 - Attempting to process with provider: deepseek
2025-07-12 22:11:42 | ERROR    | src.ai_manager:process_invoice:74 - Error with provider deepseek: DeepSeek API error: 422 - Failed to deserialize the JSON body into the target type: messages[1]: data did not match any variant of untagged enum ChatCompletionRequestContent at line 1 column 2956944
2025-07-12 22:11:42 | ERROR    | src.invoice_processor:process_single_invoice:192 - Failed to process G:\vscodeapps\augment\invoicerecorder\data\invoices\Invoice_ (3).jpg: All providers failed. Last error: DeepSeek API error: 422 - Failed to deserialize the JSON body into the target type: messages[1]: data did not match any variant of untagged enum ChatCompletionRequestContent at line 1 column 2956944
2025-07-12 22:11:42 | ERROR    | src.ai_manager:process_invoice:74 - Error with provider deepseek: DeepSeek API error: 422 - Failed to deserialize the JSON body into the target type: messages[1]: data did not match any variant of untagged enum ChatCompletionRequestContent at line 1 column 3181756
2025-07-12 22:11:42 | ERROR    | src.invoice_processor:process_single_invoice:192 - Failed to process G:\vscodeapps\augment\invoicerecorder\data\invoices\Invoice_ (2).jpg: All providers failed. Last error: DeepSeek API error: 422 - Failed to deserialize the JSON body into the target type: messages[1]: data did not match any variant of untagged enum ChatCompletionRequestContent at line 1 column 3181756
2025-07-12 22:11:45 | ERROR    | src.ai_manager:process_invoice:74 - Error with provider deepseek: DeepSeek API error: 422 - Failed to deserialize the JSON body into the target type: messages[1]: data did not match any variant of untagged enum ChatCompletionRequestContent at line 1 column 3942368
2025-07-12 22:11:45 | ERROR    | src.invoice_processor:process_single_invoice:192 - Failed to process G:\vscodeapps\augment\invoicerecorder\data\invoices\Invoice_ (1).jpg: All providers failed. Last error: DeepSeek API error: 422 - Failed to deserialize the JSON body into the target type: messages[1]: data did not match any variant of untagged enum ChatCompletionRequestContent at line 1 column 3942368
2025-07-12 22:11:57 | ERROR    | src.ai_manager:process_invoice:74 - Error with provider deepseek: DeepSeek API error: 422 - Failed to deserialize the JSON body into the target type: messages[1]: data did not match any variant of untagged enum ChatCompletionRequestContent at line 1 column 4593648
2025-07-12 22:11:57 | ERROR    | src.invoice_processor:process_single_invoice:192 - Failed to process G:\vscodeapps\augment\invoicerecorder\data\invoices\Invoice_ (4).jpg: All providers failed. Last error: DeepSeek API error: 422 - Failed to deserialize the JSON body into the target type: messages[1]: data did not match any variant of untagged enum ChatCompletionRequestContent at line 1 column 4593648
2025-07-12 22:11:57 | INFO     | src.invoice_processor:process_batch:245 - Batch processing completed: 0/4 successful
2025-07-12 22:17:01 | INFO     | src.logging_config:setup_logging:73 - Logging system initialized
2025-07-12 22:17:02 | INFO     | src.ai_manager:__init__:35 - AI Manager initialized with primary: openrouter
2025-07-12 22:17:02 | INFO     | src.ai_manager:__init__:36 - Available providers: ['deepseek', 'openrouter', 'ollama']
2025-07-12 22:17:02 | INFO     | src.invoice_processor:__init__:78 - Invoice Processor initialized
2025-07-12 22:17:02 | INFO     | src.excel_manager:__init__:40 - Excel Manager initialized
2025-07-12 22:17:02 | INFO     | src.ai_manager:__init__:35 - AI Manager initialized with primary: openrouter
2025-07-12 22:17:02 | INFO     | src.ai_manager:__init__:36 - Available providers: ['deepseek', 'openrouter', 'ollama']
2025-07-12 22:17:12 | INFO     | src.logging_config:setup_logging:73 - Logging system initialized
2025-07-12 22:17:12 | INFO     | src.ai_manager:__init__:35 - AI Manager initialized with primary: openrouter
2025-07-12 22:17:12 | INFO     | src.ai_manager:__init__:36 - Available providers: ['deepseek', 'openrouter', 'ollama']
2025-07-12 22:17:12 | INFO     | src.invoice_processor:__init__:78 - Invoice Processor initialized
2025-07-12 22:17:12 | INFO     | src.excel_manager:__init__:40 - Excel Manager initialized
2025-07-12 22:17:13 | INFO     | src.ai_manager:__init__:35 - AI Manager initialized with primary: openrouter
2025-07-12 22:17:13 | INFO     | src.ai_manager:__init__:36 - Available providers: ['deepseek', 'openrouter', 'ollama']
2025-07-12 22:17:13 | INFO     | src.invoice_processor:discover_invoice_files:275 - Discovered 4 invoice files in G:\vscodeapps\augment\invoicerecorder\data\invoices
2025-07-12 22:17:41 | INFO     | src.invoice_processor:process_batch:218 - Starting batch processing of 1 files
2025-07-12 22:17:41 | INFO     | src.invoice_processor:process_single_invoice:100 - Processing invoice: G:\vscodeapps\augment\invoicerecorder\data\invoices\Invoice_ (1).jpg
2025-07-12 22:17:42 | DEBUG    | src.utils:enhance_image_pil:104 - Enhanced image saved to: G:\vscodeapps\augment\invoicerecorder\data\processed\enhanced_Invoice_ (1).jpg
2025-07-12 22:17:42 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: openrouter
2025-07-12 22:17:45 | ERROR    | src.ai_manager:process_invoice:83 - Error with provider openrouter: OpenRouter API error: 402 - {"error":{"message":"This request requires more credits, or fewer max_tokens. You requested up to 4000 tokens, but can only afford 649. To increase, visit https://openrouter.ai/settings/credits and upgrade to a paid account","code":402,"metadata":{"provider_name":null}},"user_id":"user_2tx8armN8JKxx168S42Ir7XSphz"}
2025-07-12 22:17:45 | ERROR    | src.invoice_processor:process_single_invoice:192 - Failed to process G:\vscodeapps\augment\invoicerecorder\data\invoices\Invoice_ (1).jpg: All providers failed. Last error: OpenRouter API error: 402 - {"error":{"message":"This request requires more credits, or fewer max_tokens. You requested up to 4000 tokens, but can only afford 649. To increase, visit https://openrouter.ai/settings/credits and upgrade to a paid account","code":402,"metadata":{"provider_name":null}},"user_id":"user_2tx8armN8JKxx168S42Ir7XSphz"}
2025-07-12 22:17:45 | INFO     | src.invoice_processor:process_batch:245 - Batch processing completed: 0/1 successful
2025-07-12 22:17:45 | INFO     | src.invoice_processor:process_batch:218 - Starting batch processing of 1 files
2025-07-12 22:17:45 | INFO     | src.invoice_processor:process_single_invoice:100 - Processing invoice: G:\vscodeapps\augment\invoicerecorder\data\invoices\Invoice_ (2).jpg
2025-07-12 22:17:46 | DEBUG    | src.utils:enhance_image_pil:104 - Enhanced image saved to: G:\vscodeapps\augment\invoicerecorder\data\processed\enhanced_Invoice_ (2).jpg
2025-07-12 22:17:46 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: openrouter
2025-07-12 22:17:52 | ERROR    | src.ai_manager:process_invoice:83 - Error with provider openrouter: OpenRouter API error: 402 - {"error":{"message":"This request requires more credits, or fewer max_tokens. You requested up to 4000 tokens, but can only afford 649. To increase, visit https://openrouter.ai/settings/credits and upgrade to a paid account","code":402,"metadata":{"provider_name":null}},"user_id":"user_2tx8armN8JKxx168S42Ir7XSphz"}
2025-07-12 22:17:52 | ERROR    | src.invoice_processor:process_single_invoice:192 - Failed to process G:\vscodeapps\augment\invoicerecorder\data\invoices\Invoice_ (2).jpg: All providers failed. Last error: OpenRouter API error: 402 - {"error":{"message":"This request requires more credits, or fewer max_tokens. You requested up to 4000 tokens, but can only afford 649. To increase, visit https://openrouter.ai/settings/credits and upgrade to a paid account","code":402,"metadata":{"provider_name":null}},"user_id":"user_2tx8armN8JKxx168S42Ir7XSphz"}
2025-07-12 22:17:52 | INFO     | src.invoice_processor:process_batch:245 - Batch processing completed: 0/1 successful
2025-07-12 22:17:52 | INFO     | src.invoice_processor:process_batch:218 - Starting batch processing of 1 files
2025-07-12 22:17:52 | INFO     | src.invoice_processor:process_single_invoice:100 - Processing invoice: G:\vscodeapps\augment\invoicerecorder\data\invoices\Invoice_ (3).jpg
2025-07-12 22:17:52 | DEBUG    | src.utils:enhance_image_pil:104 - Enhanced image saved to: G:\vscodeapps\augment\invoicerecorder\data\processed\enhanced_Invoice_ (3).jpg
2025-07-12 22:17:52 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: openrouter
2025-07-12 22:17:59 | ERROR    | src.ai_manager:process_invoice:83 - Error with provider openrouter: OpenRouter API error: 402 - {"error":{"message":"This request requires more credits, or fewer max_tokens. You requested up to 4000 tokens, but can only afford 649. To increase, visit https://openrouter.ai/settings/credits and upgrade to a paid account","code":402,"metadata":{"provider_name":null}},"user_id":"user_2tx8armN8JKxx168S42Ir7XSphz"}
2025-07-12 22:17:59 | ERROR    | src.invoice_processor:process_single_invoice:192 - Failed to process G:\vscodeapps\augment\invoicerecorder\data\invoices\Invoice_ (3).jpg: All providers failed. Last error: OpenRouter API error: 402 - {"error":{"message":"This request requires more credits, or fewer max_tokens. You requested up to 4000 tokens, but can only afford 649. To increase, visit https://openrouter.ai/settings/credits and upgrade to a paid account","code":402,"metadata":{"provider_name":null}},"user_id":"user_2tx8armN8JKxx168S42Ir7XSphz"}
2025-07-12 22:17:59 | INFO     | src.invoice_processor:process_batch:245 - Batch processing completed: 0/1 successful
2025-07-12 22:17:59 | INFO     | src.invoice_processor:process_batch:218 - Starting batch processing of 1 files
2025-07-12 22:17:59 | INFO     | src.invoice_processor:process_single_invoice:100 - Processing invoice: G:\vscodeapps\augment\invoicerecorder\data\invoices\Invoice_ (4).jpg
2025-07-12 22:18:00 | DEBUG    | src.utils:enhance_image_pil:104 - Enhanced image saved to: G:\vscodeapps\augment\invoicerecorder\data\processed\enhanced_Invoice_ (4).jpg
2025-07-12 22:18:00 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: openrouter
2025-07-12 22:18:09 | ERROR    | src.ai_manager:process_invoice:83 - Error with provider openrouter: OpenRouter API error: 402 - {"error":{"message":"This request requires more credits, or fewer max_tokens. You requested up to 4000 tokens, but can only afford 649. To increase, visit https://openrouter.ai/settings/credits and upgrade to a paid account","code":402,"metadata":{"provider_name":null}},"user_id":"user_2tx8armN8JKxx168S42Ir7XSphz"}
2025-07-12 22:18:09 | ERROR    | src.invoice_processor:process_single_invoice:192 - Failed to process G:\vscodeapps\augment\invoicerecorder\data\invoices\Invoice_ (4).jpg: All providers failed. Last error: OpenRouter API error: 402 - {"error":{"message":"This request requires more credits, or fewer max_tokens. You requested up to 4000 tokens, but can only afford 649. To increase, visit https://openrouter.ai/settings/credits and upgrade to a paid account","code":402,"metadata":{"provider_name":null}},"user_id":"user_2tx8armN8JKxx168S42Ir7XSphz"}
2025-07-12 22:18:09 | INFO     | src.invoice_processor:process_batch:245 - Batch processing completed: 0/1 successful
2025-07-12 22:18:54 | INFO     | src.logging_config:setup_logging:73 - Logging system initialized
2025-07-12 22:18:55 | INFO     | src.ai_manager:__init__:35 - AI Manager initialized with primary: ollama
2025-07-12 22:18:55 | INFO     | src.ai_manager:__init__:36 - Available providers: ['deepseek', 'openrouter', 'ollama']
2025-07-12 22:18:55 | INFO     | src.invoice_processor:__init__:78 - Invoice Processor initialized
2025-07-12 22:18:55 | INFO     | src.excel_manager:__init__:40 - Excel Manager initialized
2025-07-12 22:18:55 | INFO     | src.ai_manager:__init__:35 - AI Manager initialized with primary: ollama
2025-07-12 22:18:55 | INFO     | src.ai_manager:__init__:36 - Available providers: ['deepseek', 'openrouter', 'ollama']
2025-07-12 22:18:55 | INFO     | src.invoice_processor:discover_invoice_files:275 - Discovered 4 invoice files in G:\vscodeapps\augment\invoicerecorder\data\invoices
2025-07-12 22:19:01 | INFO     | src.invoice_processor:process_batch:218 - Starting batch processing of 1 files
2025-07-12 22:19:01 | INFO     | src.invoice_processor:process_single_invoice:100 - Processing invoice: G:\vscodeapps\augment\invoicerecorder\data\invoices\Invoice_ (1).jpg
2025-07-12 22:19:02 | DEBUG    | src.utils:enhance_image_pil:104 - Enhanced image saved to: G:\vscodeapps\augment\invoicerecorder\data\processed\enhanced_Invoice_ (1).jpg
2025-07-12 22:19:02 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: ollama
2025-07-12 22:19:03 | ERROR    | src.ai_manager:process_invoice:83 - Error with provider ollama: Ollama API error: 404 - {"error":"model 'llama3.2-vision' not found"}
2025-07-12 22:19:03 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: openrouter
2025-07-12 22:19:24 | ERROR    | src.ai_manager:process_invoice:83 - Error with provider openrouter: Expecting ',' delimiter: line 58 column 10 (char 1635)
2025-07-12 22:19:24 | ERROR    | src.invoice_processor:process_single_invoice:192 - Failed to process G:\vscodeapps\augment\invoicerecorder\data\invoices\Invoice_ (1).jpg: All providers failed. Last error: Expecting ',' delimiter: line 58 column 10 (char 1635)
2025-07-12 22:19:24 | INFO     | src.invoice_processor:process_batch:245 - Batch processing completed: 0/1 successful
2025-07-12 22:19:24 | INFO     | src.invoice_processor:process_batch:218 - Starting batch processing of 1 files
2025-07-12 22:19:24 | INFO     | src.invoice_processor:process_single_invoice:100 - Processing invoice: G:\vscodeapps\augment\invoicerecorder\data\invoices\Invoice_ (2).jpg
2025-07-12 22:19:25 | DEBUG    | src.utils:enhance_image_pil:104 - Enhanced image saved to: G:\vscodeapps\augment\invoicerecorder\data\processed\enhanced_Invoice_ (2).jpg
2025-07-12 22:19:25 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: ollama
2025-07-12 22:19:25 | ERROR    | src.ai_manager:process_invoice:83 - Error with provider ollama: Ollama API error: 404 - {"error":"model 'llama3.2-vision' not found"}
2025-07-12 22:19:25 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: openrouter
2025-07-12 22:19:37 | ERROR    | src.ai_manager:process_invoice:83 - Error with provider openrouter: OpenRouter API error: 402 - {"error":{"message":"This request requires more credits, or fewer max_tokens. You requested up to 600 tokens, but can only afford 473. To increase, visit https://openrouter.ai/settings/credits and upgrade to a paid account","code":402,"metadata":{"provider_name":null}},"user_id":"user_2tx8armN8JKxx168S42Ir7XSphz"}
2025-07-12 22:19:37 | ERROR    | src.invoice_processor:process_single_invoice:192 - Failed to process G:\vscodeapps\augment\invoicerecorder\data\invoices\Invoice_ (2).jpg: All providers failed. Last error: OpenRouter API error: 402 - {"error":{"message":"This request requires more credits, or fewer max_tokens. You requested up to 600 tokens, but can only afford 473. To increase, visit https://openrouter.ai/settings/credits and upgrade to a paid account","code":402,"metadata":{"provider_name":null}},"user_id":"user_2tx8armN8JKxx168S42Ir7XSphz"}
2025-07-12 22:19:37 | INFO     | src.invoice_processor:process_batch:245 - Batch processing completed: 0/1 successful
2025-07-12 22:19:37 | INFO     | src.invoice_processor:process_batch:218 - Starting batch processing of 1 files
2025-07-12 22:19:37 | INFO     | src.invoice_processor:process_single_invoice:100 - Processing invoice: G:\vscodeapps\augment\invoicerecorder\data\invoices\Invoice_ (3).jpg
2025-07-12 22:19:38 | DEBUG    | src.utils:enhance_image_pil:104 - Enhanced image saved to: G:\vscodeapps\augment\invoicerecorder\data\processed\enhanced_Invoice_ (3).jpg
2025-07-12 22:19:38 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: ollama
2025-07-12 22:19:39 | ERROR    | src.ai_manager:process_invoice:83 - Error with provider ollama: Ollama API error: 404 - {"error":"model 'llama3.2-vision' not found"}
2025-07-12 22:19:39 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: openrouter
2025-07-12 22:19:41 | ERROR    | src.ai_manager:process_invoice:83 - Error with provider openrouter: OpenRouter API error: 402 - {"error":{"message":"This request requires more credits, or fewer max_tokens. You requested up to 600 tokens, but can only afford 473. To increase, visit https://openrouter.ai/settings/credits and upgrade to a paid account","code":402,"metadata":{"provider_name":null}},"user_id":"user_2tx8armN8JKxx168S42Ir7XSphz"}
2025-07-12 22:19:41 | ERROR    | src.invoice_processor:process_single_invoice:192 - Failed to process G:\vscodeapps\augment\invoicerecorder\data\invoices\Invoice_ (3).jpg: All providers failed. Last error: OpenRouter API error: 402 - {"error":{"message":"This request requires more credits, or fewer max_tokens. You requested up to 600 tokens, but can only afford 473. To increase, visit https://openrouter.ai/settings/credits and upgrade to a paid account","code":402,"metadata":{"provider_name":null}},"user_id":"user_2tx8armN8JKxx168S42Ir7XSphz"}
2025-07-12 22:19:41 | INFO     | src.invoice_processor:process_batch:245 - Batch processing completed: 0/1 successful
2025-07-12 22:19:41 | INFO     | src.invoice_processor:process_batch:218 - Starting batch processing of 1 files
2025-07-12 22:19:41 | INFO     | src.invoice_processor:process_single_invoice:100 - Processing invoice: G:\vscodeapps\augment\invoicerecorder\data\invoices\Invoice_ (4).jpg
2025-07-12 22:19:42 | DEBUG    | src.utils:enhance_image_pil:104 - Enhanced image saved to: G:\vscodeapps\augment\invoicerecorder\data\processed\enhanced_Invoice_ (4).jpg
2025-07-12 22:19:42 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: ollama
2025-07-12 22:19:43 | ERROR    | src.ai_manager:process_invoice:83 - Error with provider ollama: Ollama API error: 404 - {"error":"model 'llama3.2-vision' not found"}
2025-07-12 22:19:43 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: openrouter
2025-07-12 22:20:01 | ERROR    | src.ai_manager:process_invoice:83 - Error with provider openrouter: OpenRouter API error: 402 - {"error":{"message":"This request requires more credits, or fewer max_tokens. You requested up to 600 tokens, but can only afford 473. To increase, visit https://openrouter.ai/settings/credits and upgrade to a paid account","code":402,"metadata":{"provider_name":null}},"user_id":"user_2tx8armN8JKxx168S42Ir7XSphz"}
2025-07-12 22:20:01 | ERROR    | src.invoice_processor:process_single_invoice:192 - Failed to process G:\vscodeapps\augment\invoicerecorder\data\invoices\Invoice_ (4).jpg: All providers failed. Last error: OpenRouter API error: 402 - {"error":{"message":"This request requires more credits, or fewer max_tokens. You requested up to 600 tokens, but can only afford 473. To increase, visit https://openrouter.ai/settings/credits and upgrade to a paid account","code":402,"metadata":{"provider_name":null}},"user_id":"user_2tx8armN8JKxx168S42Ir7XSphz"}
2025-07-12 22:20:01 | INFO     | src.invoice_processor:process_batch:245 - Batch processing completed: 0/1 successful
2025-07-12 22:28:54 | INFO     | src.logging_config:setup_logging:73 - Logging system initialized
2025-07-12 22:28:55 | INFO     | src.ai_manager:__init__:35 - AI Manager initialized with primary: openrouter
2025-07-12 22:28:55 | INFO     | src.ai_manager:__init__:36 - Available providers: ['deepseek', 'openrouter', 'ollama']
2025-07-12 22:28:55 | INFO     | src.invoice_processor:__init__:78 - Invoice Processor initialized
2025-07-12 22:28:55 | INFO     | src.excel_manager:__init__:40 - Excel Manager initialized
2025-07-12 22:28:55 | INFO     | src.ai_manager:__init__:35 - AI Manager initialized with primary: openrouter
2025-07-12 22:28:55 | INFO     | src.ai_manager:__init__:36 - Available providers: ['deepseek', 'openrouter', 'ollama']
2025-07-12 22:29:08 | INFO     | src.logging_config:setup_logging:73 - Logging system initialized
2025-07-12 22:29:09 | INFO     | src.ai_manager:__init__:35 - AI Manager initialized with primary: openrouter
2025-07-12 22:29:09 | INFO     | src.ai_manager:__init__:36 - Available providers: ['deepseek', 'openrouter', 'ollama']
2025-07-12 22:29:09 | INFO     | src.invoice_processor:__init__:78 - Invoice Processor initialized
2025-07-12 22:29:09 | INFO     | src.excel_manager:__init__:40 - Excel Manager initialized
2025-07-12 22:29:09 | INFO     | src.ai_manager:__init__:35 - AI Manager initialized with primary: openrouter
2025-07-12 22:29:09 | INFO     | src.ai_manager:__init__:36 - Available providers: ['deepseek', 'openrouter', 'ollama']
2025-07-12 22:29:09 | INFO     | src.invoice_processor:discover_invoice_files:275 - Discovered 4 invoice files in G:\vscodeapps\augment\invoicerecorder\data\invoices
2025-07-12 22:29:13 | INFO     | src.invoice_processor:process_batch:218 - Starting batch processing of 1 files
2025-07-12 22:29:13 | INFO     | src.invoice_processor:process_single_invoice:100 - Processing invoice: G:\vscodeapps\augment\invoicerecorder\data\invoices\Invoice_ (1).jpg
2025-07-12 22:29:13 | DEBUG    | src.utils:enhance_image_pil:104 - Enhanced image saved to: G:\vscodeapps\augment\invoicerecorder\data\processed\enhanced_Invoice_ (1).jpg
2025-07-12 22:29:13 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: openrouter
2025-07-12 22:29:24 | ERROR    | src.ai_manager:process_invoice:83 - Error with provider openrouter: OpenRouter API error: 404 - {"error":{"message":"No endpoints found for qwen/qwen-2-vl-7b-instruct:free.","code":404},"user_id":"user_2tx8armN8JKxx168S42Ir7XSphz"}
2025-07-12 22:29:24 | ERROR    | src.invoice_processor:process_single_invoice:192 - Failed to process G:\vscodeapps\augment\invoicerecorder\data\invoices\Invoice_ (1).jpg: All providers failed. Last error: OpenRouter API error: 404 - {"error":{"message":"No endpoints found for qwen/qwen-2-vl-7b-instruct:free.","code":404},"user_id":"user_2tx8armN8JKxx168S42Ir7XSphz"}
2025-07-12 22:29:24 | INFO     | src.invoice_processor:process_batch:245 - Batch processing completed: 0/1 successful
2025-07-12 22:29:24 | INFO     | src.invoice_processor:process_batch:218 - Starting batch processing of 1 files
2025-07-12 22:29:24 | INFO     | src.invoice_processor:process_single_invoice:100 - Processing invoice: G:\vscodeapps\augment\invoicerecorder\data\invoices\Invoice_ (2).jpg
2025-07-12 22:29:25 | DEBUG    | src.utils:enhance_image_pil:104 - Enhanced image saved to: G:\vscodeapps\augment\invoicerecorder\data\processed\enhanced_Invoice_ (2).jpg
2025-07-12 22:29:25 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: openrouter
2025-07-12 22:29:28 | ERROR    | src.ai_manager:process_invoice:83 - Error with provider openrouter: OpenRouter API error: 404 - {"error":{"message":"No endpoints found for qwen/qwen-2-vl-7b-instruct:free.","code":404},"user_id":"user_2tx8armN8JKxx168S42Ir7XSphz"}
2025-07-12 22:29:28 | ERROR    | src.invoice_processor:process_single_invoice:192 - Failed to process G:\vscodeapps\augment\invoicerecorder\data\invoices\Invoice_ (2).jpg: All providers failed. Last error: OpenRouter API error: 404 - {"error":{"message":"No endpoints found for qwen/qwen-2-vl-7b-instruct:free.","code":404},"user_id":"user_2tx8armN8JKxx168S42Ir7XSphz"}
2025-07-12 22:29:28 | INFO     | src.invoice_processor:process_batch:245 - Batch processing completed: 0/1 successful
2025-07-12 22:29:28 | INFO     | src.invoice_processor:process_batch:218 - Starting batch processing of 1 files
2025-07-12 22:29:28 | INFO     | src.invoice_processor:process_single_invoice:100 - Processing invoice: G:\vscodeapps\augment\invoicerecorder\data\invoices\Invoice_ (3).jpg
2025-07-12 22:29:29 | DEBUG    | src.utils:enhance_image_pil:104 - Enhanced image saved to: G:\vscodeapps\augment\invoicerecorder\data\processed\enhanced_Invoice_ (3).jpg
2025-07-12 22:29:29 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: openrouter
2025-07-12 22:29:36 | ERROR    | src.ai_manager:process_invoice:83 - Error with provider openrouter: OpenRouter API error: 404 - {"error":{"message":"No endpoints found for qwen/qwen-2-vl-7b-instruct:free.","code":404},"user_id":"user_2tx8armN8JKxx168S42Ir7XSphz"}
2025-07-12 22:29:36 | ERROR    | src.invoice_processor:process_single_invoice:192 - Failed to process G:\vscodeapps\augment\invoicerecorder\data\invoices\Invoice_ (3).jpg: All providers failed. Last error: OpenRouter API error: 404 - {"error":{"message":"No endpoints found for qwen/qwen-2-vl-7b-instruct:free.","code":404},"user_id":"user_2tx8armN8JKxx168S42Ir7XSphz"}
2025-07-12 22:29:36 | INFO     | src.invoice_processor:process_batch:245 - Batch processing completed: 0/1 successful
2025-07-12 22:29:36 | INFO     | src.invoice_processor:process_batch:218 - Starting batch processing of 1 files
2025-07-12 22:29:36 | INFO     | src.invoice_processor:process_single_invoice:100 - Processing invoice: G:\vscodeapps\augment\invoicerecorder\data\invoices\Invoice_ (4).jpg
2025-07-12 22:29:37 | DEBUG    | src.utils:enhance_image_pil:104 - Enhanced image saved to: G:\vscodeapps\augment\invoicerecorder\data\processed\enhanced_Invoice_ (4).jpg
2025-07-12 22:29:37 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: openrouter
2025-07-12 22:29:41 | ERROR    | src.ai_manager:process_invoice:83 - Error with provider openrouter: OpenRouter API error: 404 - {"error":{"message":"No endpoints found for qwen/qwen-2-vl-7b-instruct:free.","code":404},"user_id":"user_2tx8armN8JKxx168S42Ir7XSphz"}
2025-07-12 22:29:41 | ERROR    | src.invoice_processor:process_single_invoice:192 - Failed to process G:\vscodeapps\augment\invoicerecorder\data\invoices\Invoice_ (4).jpg: All providers failed. Last error: OpenRouter API error: 404 - {"error":{"message":"No endpoints found for qwen/qwen-2-vl-7b-instruct:free.","code":404},"user_id":"user_2tx8armN8JKxx168S42Ir7XSphz"}
2025-07-12 22:29:41 | INFO     | src.invoice_processor:process_batch:245 - Batch processing completed: 0/1 successful
2025-07-12 22:30:00 | INFO     | src.logging_config:setup_logging:73 - Logging system initialized
2025-07-12 22:30:00 | INFO     | src.ai_manager:__init__:35 - AI Manager initialized with primary: openrouter
2025-07-12 22:30:00 | INFO     | src.ai_manager:__init__:36 - Available providers: ['deepseek', 'openrouter', 'ollama']
2025-07-12 22:30:00 | INFO     | src.invoice_processor:__init__:78 - Invoice Processor initialized
2025-07-12 22:30:00 | INFO     | src.excel_manager:__init__:40 - Excel Manager initialized
2025-07-12 22:30:01 | INFO     | src.ai_manager:__init__:35 - AI Manager initialized with primary: openrouter
2025-07-12 22:30:01 | INFO     | src.ai_manager:__init__:36 - Available providers: ['deepseek', 'openrouter', 'ollama']
2025-07-12 22:30:01 | INFO     | src.invoice_processor:discover_invoice_files:275 - Discovered 4 invoice files in G:\vscodeapps\augment\invoicerecorder\data\invoices
2025-07-12 22:30:06 | INFO     | src.invoice_processor:process_batch:218 - Starting batch processing of 1 files
2025-07-12 22:30:06 | INFO     | src.invoice_processor:process_single_invoice:100 - Processing invoice: G:\vscodeapps\augment\invoicerecorder\data\invoices\Invoice_ (1).jpg
2025-07-12 22:30:07 | DEBUG    | src.utils:enhance_image_pil:104 - Enhanced image saved to: G:\vscodeapps\augment\invoicerecorder\data\processed\enhanced_Invoice_ (1).jpg
2025-07-12 22:30:07 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: deepseek
2025-07-12 22:30:42 | SUCCESS  | src.ai_manager:process_invoice:76 - Successfully processed with deepseek
2025-07-12 22:30:42 | SUCCESS  | src.invoice_processor:process_single_invoice:173 - Successfully processed G:\vscodeapps\augment\invoicerecorder\data\invoices\Invoice_ (1).jpg in 36.07s
2025-07-12 22:30:42 | INFO     | src.invoice_processor:process_batch:245 - Batch processing completed: 1/1 successful
2025-07-12 22:30:42 | INFO     | src.invoice_processor:process_batch:218 - Starting batch processing of 1 files
2025-07-12 22:30:42 | INFO     | src.invoice_processor:process_single_invoice:100 - Processing invoice: G:\vscodeapps\augment\invoicerecorder\data\invoices\Invoice_ (2).jpg
2025-07-12 22:30:43 | DEBUG    | src.utils:enhance_image_pil:104 - Enhanced image saved to: G:\vscodeapps\augment\invoicerecorder\data\processed\enhanced_Invoice_ (2).jpg
2025-07-12 22:30:43 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: deepseek
2025-07-12 22:31:11 | SUCCESS  | src.ai_manager:process_invoice:76 - Successfully processed with deepseek
2025-07-12 22:31:11 | SUCCESS  | src.invoice_processor:process_single_invoice:173 - Successfully processed G:\vscodeapps\augment\invoicerecorder\data\invoices\Invoice_ (2).jpg in 28.69s
2025-07-12 22:31:11 | INFO     | src.invoice_processor:process_batch:245 - Batch processing completed: 1/1 successful
2025-07-12 22:31:11 | INFO     | src.invoice_processor:process_batch:218 - Starting batch processing of 1 files
2025-07-12 22:31:11 | INFO     | src.invoice_processor:process_single_invoice:100 - Processing invoice: G:\vscodeapps\augment\invoicerecorder\data\invoices\Invoice_ (3).jpg
2025-07-12 22:31:12 | DEBUG    | src.utils:enhance_image_pil:104 - Enhanced image saved to: G:\vscodeapps\augment\invoicerecorder\data\processed\enhanced_Invoice_ (3).jpg
2025-07-12 22:31:12 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: deepseek
2025-07-12 22:31:28 | SUCCESS  | src.ai_manager:process_invoice:76 - Successfully processed with deepseek
2025-07-12 22:31:28 | SUCCESS  | src.invoice_processor:process_single_invoice:173 - Successfully processed G:\vscodeapps\augment\invoicerecorder\data\invoices\Invoice_ (3).jpg in 17.54s
2025-07-12 22:31:28 | INFO     | src.invoice_processor:process_batch:245 - Batch processing completed: 1/1 successful
2025-07-12 22:31:28 | INFO     | src.invoice_processor:process_batch:218 - Starting batch processing of 1 files
2025-07-12 22:31:28 | INFO     | src.invoice_processor:process_single_invoice:100 - Processing invoice: G:\vscodeapps\augment\invoicerecorder\data\invoices\Invoice_ (4).jpg
2025-07-12 22:31:29 | DEBUG    | src.utils:enhance_image_pil:104 - Enhanced image saved to: G:\vscodeapps\augment\invoicerecorder\data\processed\enhanced_Invoice_ (4).jpg
2025-07-12 22:31:29 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: deepseek
2025-07-12 22:31:55 | WARNING  | src.ai_manager:process_invoice:80 - Low confidence result from deepseek
2025-07-12 22:31:55 | ERROR    | src.invoice_processor:process_single_invoice:192 - Failed to process G:\vscodeapps\augment\invoicerecorder\data\invoices\Invoice_ (4).jpg: All providers failed. Last error: None
2025-07-12 22:31:55 | INFO     | src.invoice_processor:process_batch:245 - Batch processing completed: 0/1 successful
2025-07-12 22:31:55 | SUCCESS  | src.excel_manager:save_invoice_data:76 - Invoice data saved to: G:\vscodeapps\augment\invoicerecorder\data\output\Invoices_2025.xlsx
2025-07-12 22:50:24 | INFO     | src.logging_config:setup_logging:73 - Logging system initialized
2025-07-12 22:50:25 | INFO     | src.ai_manager:__init__:35 - AI Manager initialized with primary: openrouter
2025-07-12 22:50:25 | INFO     | src.ai_manager:__init__:36 - Available providers: ['deepseek', 'openrouter', 'ollama']
2025-07-12 22:50:25 | INFO     | src.invoice_processor:__init__:78 - Invoice Processor initialized
2025-07-12 22:50:25 | INFO     | src.excel_manager:__init__:40 - Excel Manager initialized
2025-07-12 22:50:25 | INFO     | src.ai_manager:__init__:35 - AI Manager initialized with primary: openrouter
2025-07-12 22:50:25 | INFO     | src.ai_manager:__init__:36 - Available providers: ['deepseek', 'openrouter', 'ollama']
2025-07-12 22:50:25 | INFO     | src.invoice_processor:discover_invoice_files:275 - Discovered 4 invoice files in G:\vscodeapps\augment\invoicerecorder\data\invoices
2025-07-12 22:50:29 | INFO     | src.invoice_processor:process_batch:218 - Starting batch processing of 2 files
2025-07-12 22:50:29 | INFO     | src.invoice_processor:process_single_invoice:100 - Processing invoice: G:\vscodeapps\augment\invoicerecorder\data\invoices\Invoice_ (1).jpg
2025-07-12 22:50:30 | DEBUG    | src.utils:enhance_image_pil:104 - Enhanced image saved to: G:\vscodeapps\augment\invoicerecorder\data\processed\enhanced_Invoice_ (1).jpg
2025-07-12 22:50:30 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: deepseek
2025-07-12 22:50:34 | INFO     | src.invoice_processor:process_single_invoice:100 - Processing invoice: G:\vscodeapps\augment\invoicerecorder\data\invoices\Invoice_ (2).jpg
2025-07-12 22:50:35 | DEBUG    | src.utils:enhance_image_pil:104 - Enhanced image saved to: G:\vscodeapps\augment\invoicerecorder\data\processed\enhanced_Invoice_ (2).jpg
2025-07-12 22:50:35 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: deepseek
2025-07-12 22:50:57 | SUCCESS  | src.ai_manager:process_invoice:76 - Successfully processed with deepseek
2025-07-12 22:50:57 | SUCCESS  | src.invoice_processor:process_single_invoice:173 - Successfully processed G:\vscodeapps\augment\invoicerecorder\data\invoices\Invoice_ (2).jpg in 22.90s
2025-07-12 22:51:07 | SUCCESS  | src.ai_manager:process_invoice:76 - Successfully processed with deepseek
2025-07-12 22:51:07 | SUCCESS  | src.invoice_processor:process_single_invoice:173 - Successfully processed G:\vscodeapps\augment\invoicerecorder\data\invoices\Invoice_ (1).jpg in 37.41s
2025-07-12 22:51:07 | INFO     | src.invoice_processor:process_batch:245 - Batch processing completed: 2/2 successful
2025-07-12 22:51:07 | INFO     | src.invoice_processor:process_batch:218 - Starting batch processing of 2 files
2025-07-12 22:51:07 | INFO     | src.invoice_processor:process_single_invoice:100 - Processing invoice: G:\vscodeapps\augment\invoicerecorder\data\invoices\Invoice_ (3).jpg
2025-07-12 22:51:07 | DEBUG    | src.utils:enhance_image_pil:104 - Enhanced image saved to: G:\vscodeapps\augment\invoicerecorder\data\processed\enhanced_Invoice_ (3).jpg
2025-07-12 22:51:07 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: deepseek
2025-07-12 22:51:09 | INFO     | src.invoice_processor:process_single_invoice:100 - Processing invoice: G:\vscodeapps\augment\invoicerecorder\data\invoices\Invoice_ (4).jpg
2025-07-12 22:51:10 | DEBUG    | src.utils:enhance_image_pil:104 - Enhanced image saved to: G:\vscodeapps\augment\invoicerecorder\data\processed\enhanced_Invoice_ (4).jpg
2025-07-12 22:51:10 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: deepseek
2025-07-12 22:51:27 | SUCCESS  | src.ai_manager:process_invoice:76 - Successfully processed with deepseek
2025-07-12 22:51:27 | ERROR    | src.invoice_processor:process_single_invoice:192 - Failed to process G:\vscodeapps\augment\invoicerecorder\data\invoices\Invoice_ (3).jpg: 'NoneType' object has no attribute 'upper'
2025-07-12 22:51:30 | SUCCESS  | src.ai_manager:process_invoice:76 - Successfully processed with deepseek
2025-07-12 22:51:30 | SUCCESS  | src.invoice_processor:process_single_invoice:173 - Successfully processed G:\vscodeapps\augment\invoicerecorder\data\invoices\Invoice_ (4).jpg in 20.71s
2025-07-12 22:51:30 | INFO     | src.invoice_processor:process_batch:245 - Batch processing completed: 1/2 successful
2025-07-12 22:51:30 | SUCCESS  | src.excel_manager:save_invoice_data:82 - Invoice data saved to: G:\vscodeapps\augment\invoicerecorder\data\output\Invoices_2025.xlsx
2025-07-12 22:52:26 | INFO     | src.logging_config:setup_logging:73 - Logging system initialized
2025-07-12 22:52:27 | INFO     | src.ai_manager:__init__:35 - AI Manager initialized with primary: openrouter
2025-07-12 22:52:27 | INFO     | src.ai_manager:__init__:36 - Available providers: ['deepseek', 'openrouter', 'ollama']
2025-07-12 22:52:27 | INFO     | src.invoice_processor:__init__:78 - Invoice Processor initialized
2025-07-12 22:52:27 | INFO     | src.excel_manager:__init__:40 - Excel Manager initialized
2025-07-12 22:52:27 | INFO     | src.ai_manager:__init__:35 - AI Manager initialized with primary: openrouter
2025-07-12 22:52:27 | INFO     | src.ai_manager:__init__:36 - Available providers: ['deepseek', 'openrouter', 'ollama']
2025-07-12 22:52:27 | INFO     | src.invoice_processor:discover_invoice_files:275 - Discovered 4 invoice files in data\invoices
2025-07-12 22:52:30 | INFO     | src.invoice_processor:process_batch:218 - Starting batch processing of 1 files
2025-07-12 22:52:30 | INFO     | src.invoice_processor:process_single_invoice:100 - Processing invoice: data\invoices\Invoice_ (1).jpg
2025-07-12 22:52:31 | DEBUG    | src.utils:enhance_image_pil:104 - Enhanced image saved to: G:\vscodeapps\augment\invoicerecorder\data\processed\enhanced_Invoice_ (1).jpg
2025-07-12 22:52:31 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: deepseek
2025-07-12 22:53:04 | SUCCESS  | src.ai_manager:process_invoice:76 - Successfully processed with deepseek
2025-07-12 22:53:04 | SUCCESS  | src.invoice_processor:process_single_invoice:173 - Successfully processed data\invoices\Invoice_ (1).jpg in 33.93s
2025-07-12 22:53:04 | INFO     | src.invoice_processor:process_batch:245 - Batch processing completed: 1/1 successful
2025-07-12 22:53:04 | INFO     | src.invoice_processor:process_batch:218 - Starting batch processing of 1 files
2025-07-12 22:53:04 | INFO     | src.invoice_processor:process_single_invoice:100 - Processing invoice: data\invoices\Invoice_ (2).jpg
2025-07-12 22:53:05 | DEBUG    | src.utils:enhance_image_pil:104 - Enhanced image saved to: G:\vscodeapps\augment\invoicerecorder\data\processed\enhanced_Invoice_ (2).jpg
2025-07-12 22:53:05 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: deepseek
2025-07-12 22:53:18 | SUCCESS  | src.ai_manager:process_invoice:76 - Successfully processed with deepseek
2025-07-12 22:53:18 | SUCCESS  | src.invoice_processor:process_single_invoice:173 - Successfully processed data\invoices\Invoice_ (2).jpg in 13.86s
2025-07-12 22:53:18 | INFO     | src.invoice_processor:process_batch:245 - Batch processing completed: 1/1 successful
2025-07-12 22:53:18 | INFO     | src.invoice_processor:process_batch:218 - Starting batch processing of 1 files
2025-07-12 22:53:18 | INFO     | src.invoice_processor:process_single_invoice:100 - Processing invoice: data\invoices\Invoice_ (3).jpg
2025-07-12 22:53:19 | DEBUG    | src.utils:enhance_image_pil:104 - Enhanced image saved to: G:\vscodeapps\augment\invoicerecorder\data\processed\enhanced_Invoice_ (3).jpg
2025-07-12 22:53:19 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: deepseek
2025-07-12 22:57:15 | INFO     | src.logging_config:setup_logging:73 - Logging system initialized
2025-07-12 23:00:52 | INFO     | src.logging_config:setup_logging:73 - Logging system initialized
2025-07-12 23:00:53 | INFO     | src.ai_manager:__init__:35 - AI Manager initialized with primary: openrouter
2025-07-12 23:00:53 | INFO     | src.ai_manager:__init__:36 - Available providers: ['deepseek', 'openrouter', 'ollama']
2025-07-12 23:00:53 | INFO     | src.invoice_processor:__init__:78 - Invoice Processor initialized
2025-07-12 23:00:53 | INFO     | src.excel_manager:__init__:40 - Excel Manager initialized
2025-07-12 23:00:53 | INFO     | src.ai_manager:__init__:35 - AI Manager initialized with primary: openrouter
2025-07-12 23:00:53 | INFO     | src.ai_manager:__init__:36 - Available providers: ['deepseek', 'openrouter', 'ollama']
2025-07-12 23:00:53 | INFO     | src.invoice_processor:discover_invoice_files:275 - Discovered 4 invoice files in G:\vscodeapps\augment\invoicerecorder\data\invoices
2025-07-12 23:00:56 | INFO     | src.invoice_processor:process_batch:218 - Starting batch processing of 4 files
2025-07-12 23:00:56 | INFO     | src.invoice_processor:process_single_invoice:100 - Processing invoice: G:\vscodeapps\augment\invoicerecorder\data\invoices\Invoice_ (1).jpg
2025-07-12 23:00:57 | DEBUG    | src.utils:enhance_image_pil:104 - Enhanced image saved to: G:\vscodeapps\augment\invoicerecorder\data\processed\enhanced_Invoice_ (1).jpg
2025-07-12 23:00:57 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: openrouter
2025-07-12 23:00:57 | INFO     | src.invoice_processor:process_single_invoice:100 - Processing invoice: G:\vscodeapps\augment\invoicerecorder\data\invoices\Invoice_ (2).jpg
2025-07-12 23:00:58 | DEBUG    | src.utils:enhance_image_pil:104 - Enhanced image saved to: G:\vscodeapps\augment\invoicerecorder\data\processed\enhanced_Invoice_ (2).jpg
2025-07-12 23:00:58 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: openrouter
2025-07-12 23:00:58 | INFO     | src.invoice_processor:process_single_invoice:100 - Processing invoice: G:\vscodeapps\augment\invoicerecorder\data\invoices\Invoice_ (3).jpg
2025-07-12 23:00:59 | DEBUG    | src.utils:enhance_image_pil:104 - Enhanced image saved to: G:\vscodeapps\augment\invoicerecorder\data\processed\enhanced_Invoice_ (3).jpg
2025-07-12 23:00:59 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: openrouter
2025-07-12 23:00:59 | INFO     | src.invoice_processor:process_single_invoice:100 - Processing invoice: G:\vscodeapps\augment\invoicerecorder\data\invoices\Invoice_ (4).jpg
2025-07-12 23:01:00 | DEBUG    | src.utils:enhance_image_pil:104 - Enhanced image saved to: G:\vscodeapps\augment\invoicerecorder\data\processed\enhanced_Invoice_ (4).jpg
2025-07-12 23:01:00 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: openrouter
2025-07-12 23:01:04 | ERROR    | src.ai_manager:process_invoice:83 - Error with provider openrouter: OpenRouter API error: 404 - {"error":{"message":"No endpoints found for qwen/qwen-2-vl-7b-instruct:free.","code":404},"user_id":"user_2tx8armN8JKxx168S42Ir7XSphz"}
2025-07-12 23:01:04 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: deepseek
2025-07-12 23:01:06 | ERROR    | src.ai_manager:process_invoice:83 - Error with provider openrouter: OpenRouter API error: 404 - {"error":{"message":"No endpoints found for qwen/qwen-2-vl-7b-instruct:free.","code":404},"user_id":"user_2tx8armN8JKxx168S42Ir7XSphz"}
2025-07-12 23:01:06 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: deepseek
2025-07-12 23:01:09 | ERROR    | src.ai_manager:process_invoice:83 - Error with provider openrouter: OpenRouter API error: 404 - {"error":{"message":"No endpoints found for qwen/qwen-2-vl-7b-instruct:free.","code":404},"user_id":"user_2tx8armN8JKxx168S42Ir7XSphz"}
2025-07-12 23:01:09 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: deepseek
2025-07-12 23:01:12 | ERROR    | src.ai_manager:process_invoice:83 - Error with provider openrouter: OpenRouter API error: 404 - {"error":{"message":"No endpoints found for qwen/qwen-2-vl-7b-instruct:free.","code":404},"user_id":"user_2tx8armN8JKxx168S42Ir7XSphz"}
2025-07-12 23:01:12 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: deepseek
2025-07-12 23:01:26 | SUCCESS  | src.ai_manager:process_invoice:76 - Successfully processed with deepseek
2025-07-12 23:01:26 | SUCCESS  | src.invoice_processor:process_single_invoice:173 - Successfully processed G:\vscodeapps\augment\invoicerecorder\data\invoices\Invoice_ (2).jpg in 29.28s
2025-07-12 23:01:30 | SUCCESS  | src.ai_manager:process_invoice:76 - Successfully processed with deepseek
2025-07-12 23:01:30 | SUCCESS  | src.invoice_processor:process_single_invoice:173 - Successfully processed G:\vscodeapps\augment\invoicerecorder\data\invoices\Invoice_ (3).jpg in 32.13s
2025-07-12 23:01:31 | SUCCESS  | src.ai_manager:process_invoice:76 - Successfully processed with deepseek
2025-07-12 23:01:31 | SUCCESS  | src.invoice_processor:process_single_invoice:173 - Successfully processed G:\vscodeapps\augment\invoicerecorder\data\invoices\Invoice_ (4).jpg in 32.17s
2025-07-12 23:01:54 | SUCCESS  | src.ai_manager:process_invoice:76 - Successfully processed with deepseek
2025-07-12 23:01:54 | SUCCESS  | src.invoice_processor:process_single_invoice:173 - Successfully processed G:\vscodeapps\augment\invoicerecorder\data\invoices\Invoice_ (1).jpg in 57.76s
2025-07-12 23:01:54 | INFO     | src.invoice_processor:process_batch:245 - Batch processing completed: 4/4 successful
2025-07-12 23:01:54 | SUCCESS  | src.excel_manager:save_invoice_data:82 - Invoice data saved to: G:\vscodeapps\augment\invoicerecorder\data\output\Invoices_2025.xlsx
2025-07-12 23:10:40 | INFO     | src.logging_config:setup_logging:73 - Logging system initialized
2025-07-12 23:11:14 | INFO     | src.ai_manager:__init__:35 - AI Manager initialized with primary: openrouter
2025-07-12 23:11:14 | INFO     | src.ai_manager:__init__:36 - Available providers: ['deepseek', 'openrouter', 'ollama']
2025-07-12 23:11:14 | INFO     | src.invoice_processor:__init__:78 - Invoice Processor initialized
2025-07-12 23:11:14 | INFO     | src.excel_manager:__init__:40 - Excel Manager initialized
2025-07-12 23:11:14 | INFO     | src.ai_manager:__init__:35 - AI Manager initialized with primary: openrouter
2025-07-12 23:11:14 | INFO     | src.ai_manager:__init__:36 - Available providers: ['deepseek', 'openrouter', 'ollama']
2025-07-12 23:54:50 | INFO     | src.logging_config:setup_logging:73 - Logging system initialized
2025-07-12 23:54:50 | INFO     | src.ai_manager:__init__:35 - AI Manager initialized with primary: openrouter
2025-07-12 23:54:50 | INFO     | src.ai_manager:__init__:36 - Available providers: ['deepseek', 'openrouter', 'ollama']
2025-07-12 23:54:50 | INFO     | src.invoice_processor:__init__:78 - Invoice Processor initialized
2025-07-12 23:54:50 | INFO     | src.excel_manager:__init__:40 - Excel Manager initialized
2025-07-12 23:54:51 | INFO     | src.ai_manager:__init__:35 - AI Manager initialized with primary: openrouter
2025-07-12 23:54:51 | INFO     | src.ai_manager:__init__:36 - Available providers: ['deepseek', 'openrouter', 'ollama']
2025-07-12 23:54:51 | INFO     | src.invoice_processor:discover_invoice_files:275 - Discovered 1 invoice files in data\invoices
2025-07-12 23:54:54 | INFO     | src.invoice_processor:process_batch:218 - Starting batch processing of 1 files
2025-07-12 23:54:54 | INFO     | src.invoice_processor:process_single_invoice:100 - Processing invoice: data\invoices\Invoice-391B180F-0023.pdf
2025-07-12 23:54:54 | ERROR    | src.utils:convert_pdf_to_images:408 - Error converting PDF data\invoices\Invoice-391B180F-0023.pdf: Unable to get page count. Is poppler installed and in PATH?
2025-07-12 23:54:54 | ERROR    | src.invoice_processor:process_single_invoice:192 - Failed to process data\invoices\Invoice-391B180F-0023.pdf: Failed to convert PDF to images
2025-07-12 23:54:54 | INFO     | src.invoice_processor:process_batch:245 - Batch processing completed: 0/1 successful
2025-07-12 23:57:02 | INFO     | src.logging_config:setup_logging:73 - Logging system initialized
2025-07-12 23:57:02 | INFO     | src.ai_manager:__init__:35 - AI Manager initialized with primary: openrouter
2025-07-12 23:57:02 | INFO     | src.ai_manager:__init__:36 - Available providers: ['deepseek', 'openrouter', 'ollama']
2025-07-12 23:57:02 | INFO     | src.invoice_processor:__init__:78 - Invoice Processor initialized
2025-07-12 23:57:02 | INFO     | src.excel_manager:__init__:40 - Excel Manager initialized
2025-07-12 23:57:03 | INFO     | src.ai_manager:__init__:35 - AI Manager initialized with primary: openrouter
2025-07-12 23:57:03 | INFO     | src.ai_manager:__init__:36 - Available providers: ['deepseek', 'openrouter', 'ollama']
2025-07-12 23:57:03 | INFO     | src.invoice_processor:discover_invoice_files:275 - Discovered 1 invoice files in data\invoices
2025-07-12 23:57:04 | INFO     | src.invoice_processor:process_batch:218 - Starting batch processing of 1 files
2025-07-12 23:57:04 | INFO     | src.invoice_processor:process_single_invoice:100 - Processing invoice: data\invoices\Invoice-391B180F-0023.png
2025-07-12 23:57:05 | DEBUG    | src.utils:enhance_image_pil:104 - Enhanced image saved to: G:\vscodeapps\augment\invoicekeeper\data\processed\enhanced_Invoice-391B180F-0023.png
2025-07-12 23:57:05 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: openrouter
2025-07-12 23:57:07 | ERROR    | src.ai_manager:process_invoice:83 - Error with provider openrouter: OpenRouter API error: 404 - {"error":{"message":"No endpoints found for qwen/qwen-2-vl-7b-instruct:free.","code":404},"user_id":"user_2tx8armN8JKxx168S42Ir7XSphz"}
2025-07-12 23:57:07 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: deepseek
2025-07-12 23:57:19 | SUCCESS  | src.ai_manager:process_invoice:76 - Successfully processed with deepseek
2025-07-12 23:57:19 | SUCCESS  | src.invoice_processor:process_single_invoice:173 - Successfully processed data\invoices\Invoice-391B180F-0023.png in 15.17s
2025-07-12 23:57:19 | INFO     | src.invoice_processor:process_batch:245 - Batch processing completed: 1/1 successful
2025-07-12 23:57:19 | SUCCESS  | src.excel_manager:save_invoice_data:82 - Invoice data saved to: G:\vscodeapps\augment\invoicekeeper\data\output\Invoices_2025.xlsx
2025-07-13 11:13:51 | INFO     | src.logging_config:setup_logging:73 - Logging system initialized
2025-07-13 11:13:52 | INFO     | src.ai_manager:__init__:35 - AI Manager initialized with primary: openrouter
2025-07-13 11:13:52 | INFO     | src.ai_manager:__init__:36 - Available providers: ['deepseek', 'openrouter', 'ollama']
2025-07-13 11:13:52 | INFO     | src.invoice_processor:__init__:78 - Invoice Processor initialized
2025-07-13 11:13:52 | INFO     | src.excel_manager:__init__:40 - Excel Manager initialized
2025-07-13 11:13:52 | INFO     | src.ai_manager:__init__:35 - AI Manager initialized with primary: openrouter
2025-07-13 11:13:52 | INFO     | src.ai_manager:__init__:36 - Available providers: ['deepseek', 'openrouter', 'ollama']
2025-07-13 11:13:52 | INFO     | src.invoice_processor:discover_invoice_files:275 - Discovered 2 invoice files in G:\vscodeapps\augment\invoicekeeper\data\invoices
2025-07-13 11:13:55 | INFO     | src.invoice_processor:process_batch:218 - Starting batch processing of 2 files
2025-07-13 11:13:55 | INFO     | src.invoice_processor:process_single_invoice:100 - Processing invoice: G:\vscodeapps\augment\invoicekeeper\data\invoices\Invoice-1.png
2025-07-13 11:13:55 | DEBUG    | src.utils:enhance_image_pil:104 - Enhanced image saved to: G:\vscodeapps\augment\invoicekeeper\data\processed\enhanced_Invoice-1.png
2025-07-13 11:13:55 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: openrouter
2025-07-13 11:13:56 | INFO     | src.invoice_processor:process_single_invoice:100 - Processing invoice: G:\vscodeapps\augment\invoicekeeper\data\invoices\Invoice-2.jpg
2025-07-13 11:13:56 | DEBUG    | src.utils:enhance_image_pil:104 - Enhanced image saved to: G:\vscodeapps\augment\invoicekeeper\data\processed\enhanced_Invoice-2.jpg
2025-07-13 11:13:56 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: openrouter
2025-07-13 11:13:58 | ERROR    | src.ai_manager:process_invoice:83 - Error with provider openrouter: OpenRouter API error: 404 - {"error":{"message":"No endpoints found for qwen/qwen-2-vl-7b-instruct:free.","code":404},"user_id":"user_2tx8armN8JKxx168S42Ir7XSphz"}
2025-07-13 11:13:58 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: deepseek
2025-07-13 11:14:01 | ERROR    | src.ai_manager:process_invoice:83 - Error with provider openrouter: OpenRouter API error: 404 - {"error":{"message":"No endpoints found for qwen/qwen-2-vl-7b-instruct:free.","code":404},"user_id":"user_2tx8armN8JKxx168S42Ir7XSphz"}
2025-07-13 11:14:01 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: deepseek
2025-07-13 11:14:13 | SUCCESS  | src.ai_manager:process_invoice:76 - Successfully processed with deepseek
2025-07-13 11:14:13 | SUCCESS  | src.invoice_processor:process_single_invoice:173 - Successfully processed G:\vscodeapps\augment\invoicekeeper\data\invoices\Invoice-1.png in 18.27s
2025-07-13 11:14:27 | SUCCESS  | src.ai_manager:process_invoice:76 - Successfully processed with deepseek
2025-07-13 11:14:27 | SUCCESS  | src.invoice_processor:process_single_invoice:173 - Successfully processed G:\vscodeapps\augment\invoicekeeper\data\invoices\Invoice-2.jpg in 31.81s
2025-07-13 11:14:27 | INFO     | src.invoice_processor:process_batch:245 - Batch processing completed: 2/2 successful
2025-07-13 11:14:27 | SUCCESS  | src.excel_manager:save_invoice_data:82 - Invoice data saved to: G:\vscodeapps\augment\invoicekeeper\data\output\Invoices_2025.xlsx
2025-07-13 11:19:06 | INFO     | src.logging_config:setup_logging:73 - Logging system initialized
2025-07-13 11:19:07 | INFO     | src.ai_manager:__init__:35 - AI Manager initialized with primary: openrouter
2025-07-13 11:19:07 | INFO     | src.ai_manager:__init__:36 - Available providers: ['deepseek', 'openrouter', 'ollama']
2025-07-13 11:19:07 | INFO     | src.invoice_processor:__init__:78 - Invoice Processor initialized
2025-07-13 11:19:07 | INFO     | src.excel_manager:__init__:40 - Excel Manager initialized
2025-07-13 11:19:07 | INFO     | src.ai_manager:__init__:35 - AI Manager initialized with primary: openrouter
2025-07-13 11:19:07 | INFO     | src.ai_manager:__init__:36 - Available providers: ['deepseek', 'openrouter', 'ollama']
2025-07-13 11:19:07 | INFO     | src.invoice_processor:discover_invoice_files:275 - Discovered 4 invoice files in G:\vscodeapps\augment\invoicekeeper\data\invoices
2025-07-13 11:19:13 | INFO     | src.invoice_processor:process_batch:218 - Starting batch processing of 4 files
2025-07-13 11:19:13 | INFO     | src.invoice_processor:process_single_invoice:100 - Processing invoice: G:\vscodeapps\augment\invoicekeeper\data\invoices\Invoice-1.png
2025-07-13 11:19:13 | DEBUG    | src.utils:enhance_image_pil:104 - Enhanced image saved to: G:\vscodeapps\augment\invoicekeeper\data\processed\enhanced_Invoice-1.png
2025-07-13 11:19:13 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: openrouter
2025-07-13 11:19:14 | INFO     | src.invoice_processor:process_single_invoice:100 - Processing invoice: G:\vscodeapps\augment\invoicekeeper\data\invoices\Invoice-2.jpg
2025-07-13 11:19:14 | DEBUG    | src.utils:enhance_image_pil:104 - Enhanced image saved to: G:\vscodeapps\augment\invoicekeeper\data\processed\enhanced_Invoice-2.jpg
2025-07-13 11:19:14 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: openrouter
2025-07-13 11:19:15 | INFO     | src.invoice_processor:process_single_invoice:100 - Processing invoice: G:\vscodeapps\augment\invoicekeeper\data\invoices\Invoice-3.jpg
2025-07-13 11:19:15 | DEBUG    | src.utils:enhance_image_pil:104 - Enhanced image saved to: G:\vscodeapps\augment\invoicekeeper\data\processed\enhanced_Invoice-3.jpg
2025-07-13 11:19:16 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: openrouter
2025-07-13 11:19:16 | INFO     | src.invoice_processor:process_single_invoice:100 - Processing invoice: G:\vscodeapps\augment\invoicekeeper\data\invoices\Invoice-4.jpg
2025-07-13 11:19:17 | DEBUG    | src.utils:enhance_image_pil:104 - Enhanced image saved to: G:\vscodeapps\augment\invoicekeeper\data\processed\enhanced_Invoice-4.jpg
2025-07-13 11:19:17 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: openrouter
2025-07-13 11:19:19 | ERROR    | src.ai_manager:process_invoice:83 - Error with provider openrouter: OpenRouter API error: 404 - {"error":{"message":"No endpoints found for qwen/qwen-2-vl-7b-instruct:free.","code":404},"user_id":"user_2tx8armN8JKxx168S42Ir7XSphz"}
2025-07-13 11:19:19 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: deepseek
2025-07-13 11:19:21 | ERROR    | src.ai_manager:process_invoice:83 - Error with provider openrouter: OpenRouter API error: 404 - {"error":{"message":"No endpoints found for qwen/qwen-2-vl-7b-instruct:free.","code":404},"user_id":"user_2tx8armN8JKxx168S42Ir7XSphz"}
2025-07-13 11:19:21 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: deepseek
2025-07-13 11:19:23 | ERROR    | src.ai_manager:process_invoice:83 - Error with provider openrouter: OpenRouter API error: 404 - {"error":{"message":"No endpoints found for qwen/qwen-2-vl-7b-instruct:free.","code":404},"user_id":"user_2tx8armN8JKxx168S42Ir7XSphz"}
2025-07-13 11:19:23 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: deepseek
2025-07-13 11:19:25 | ERROR    | src.ai_manager:process_invoice:83 - Error with provider openrouter: OpenRouter API error: 404 - {"error":{"message":"No endpoints found for qwen/qwen-2-vl-7b-instruct:free.","code":404},"user_id":"user_2tx8armN8JKxx168S42Ir7XSphz"}
2025-07-13 11:19:25 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: deepseek
2025-07-13 11:19:37 | SUCCESS  | src.ai_manager:process_invoice:76 - Successfully processed with deepseek
2025-07-13 11:19:37 | SUCCESS  | src.invoice_processor:process_single_invoice:173 - Successfully processed G:\vscodeapps\augment\invoicekeeper\data\invoices\Invoice-3.jpg in 22.24s
2025-07-13 11:19:38 | SUCCESS  | src.ai_manager:process_invoice:76 - Successfully processed with deepseek
2025-07-13 11:19:38 | SUCCESS  | src.invoice_processor:process_single_invoice:173 - Successfully processed G:\vscodeapps\augment\invoicekeeper\data\invoices\Invoice-1.png in 25.26s
2025-07-13 11:19:39 | SUCCESS  | src.ai_manager:process_invoice:76 - Successfully processed with deepseek
2025-07-13 11:19:39 | SUCCESS  | src.invoice_processor:process_single_invoice:173 - Successfully processed G:\vscodeapps\augment\invoicekeeper\data\invoices\Invoice-4.jpg in 23.54s
2025-07-13 11:19:53 | SUCCESS  | src.ai_manager:process_invoice:76 - Successfully processed with deepseek
2025-07-13 11:19:53 | SUCCESS  | src.invoice_processor:process_single_invoice:173 - Successfully processed G:\vscodeapps\augment\invoicekeeper\data\invoices\Invoice-2.jpg in 39.11s
2025-07-13 11:19:53 | INFO     | src.invoice_processor:process_batch:245 - Batch processing completed: 4/4 successful
2025-07-13 11:19:53 | INFO     | src.excel_manager:_remove_duplicate_invoices:569 - Removed 1 duplicate entries
2025-07-13 11:19:53 | INFO     | src.excel_manager:_remove_duplicate_invoices:569 - Removed 17 duplicate entries
2025-07-13 11:19:53 | SUCCESS  | src.excel_manager:save_invoice_data:82 - Invoice data saved to: G:\vscodeapps\augment\invoicekeeper\data\output\Invoices_2025.xlsx
2025-07-13 11:47:37 | INFO     | src.logging_config:setup_logging:73 - Logging system initialized
2025-07-13 11:47:37 | INFO     | src.ai_manager:__init__:35 - AI Manager initialized with primary: openrouter
2025-07-13 11:47:37 | INFO     | src.ai_manager:__init__:36 - Available providers: ['deepseek', 'openrouter', 'ollama']
2025-07-13 11:47:37 | INFO     | src.invoice_processor:__init__:78 - Invoice Processor initialized
2025-07-13 11:47:37 | INFO     | src.excel_manager:__init__:40 - Excel Manager initialized
2025-07-13 11:47:38 | INFO     | src.ai_manager:__init__:35 - AI Manager initialized with primary: openrouter
2025-07-13 11:47:38 | INFO     | src.ai_manager:__init__:36 - Available providers: ['deepseek', 'openrouter', 'ollama']
2025-07-13 11:47:38 | INFO     | src.invoice_processor:discover_invoice_files:275 - Discovered 2 invoice files in G:\vscodeapps\augment\invoicekeeper\data\invoices
2025-07-13 11:47:41 | INFO     | src.invoice_processor:process_batch:218 - Starting batch processing of 2 files
2025-07-13 11:47:41 | INFO     | src.invoice_processor:process_single_invoice:100 - Processing invoice: G:\vscodeapps\augment\invoicekeeper\data\invoices\Invoice-1.png
2025-07-13 11:47:41 | DEBUG    | src.utils:enhance_image_pil:104 - Enhanced image saved to: G:\vscodeapps\augment\invoicekeeper\data\processed\enhanced_Invoice-1.png
2025-07-13 11:47:41 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: openrouter
2025-07-13 11:47:42 | INFO     | src.invoice_processor:process_single_invoice:100 - Processing invoice: G:\vscodeapps\augment\invoicekeeper\data\invoices\Invoice-2.jpg
2025-07-13 11:47:43 | DEBUG    | src.utils:enhance_image_pil:104 - Enhanced image saved to: G:\vscodeapps\augment\invoicekeeper\data\processed\enhanced_Invoice-2.jpg
2025-07-13 11:47:43 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: openrouter
2025-07-13 11:47:45 | ERROR    | src.ai_manager:process_invoice:83 - Error with provider openrouter: OpenRouter API error: 404 - {"error":{"message":"No endpoints found for qwen/qwen-2-vl-7b-instruct:free.","code":404},"user_id":"user_2tx8armN8JKxx168S42Ir7XSphz"}
2025-07-13 11:47:45 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: deepseek
2025-07-13 11:47:47 | ERROR    | src.ai_manager:process_invoice:83 - Error with provider openrouter: OpenRouter API error: 404 - {"error":{"message":"No endpoints found for qwen/qwen-2-vl-7b-instruct:free.","code":404},"user_id":"user_2tx8armN8JKxx168S42Ir7XSphz"}
2025-07-13 11:47:47 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: deepseek
2025-07-13 11:47:59 | SUCCESS  | src.ai_manager:process_invoice:76 - Successfully processed with deepseek
2025-07-13 11:47:59 | SUCCESS  | src.invoice_processor:process_single_invoice:173 - Successfully processed G:\vscodeapps\augment\invoicekeeper\data\invoices\Invoice-1.png in 18.03s
2025-07-13 11:48:15 | SUCCESS  | src.ai_manager:process_invoice:76 - Successfully processed with deepseek
2025-07-13 11:48:15 | SUCCESS  | src.invoice_processor:process_single_invoice:173 - Successfully processed G:\vscodeapps\augment\invoicekeeper\data\invoices\Invoice-2.jpg in 33.08s
2025-07-13 11:48:15 | INFO     | src.invoice_processor:process_batch:245 - Batch processing completed: 2/2 successful
2025-07-13 11:48:15 | SUCCESS  | src.excel_manager:save_invoice_data:82 - Invoice data saved to: G:\vscodeapps\augment\invoicekeeper\data\output\Invoices_2025.xlsx
2025-07-13 11:52:28 | INFO     | src.logging_config:setup_logging:73 - Logging system initialized
2025-07-13 11:52:29 | INFO     | src.ai_manager:__init__:35 - AI Manager initialized with primary: openrouter
2025-07-13 11:52:29 | INFO     | src.ai_manager:__init__:36 - Available providers: ['deepseek', 'openrouter', 'ollama']
2025-07-13 11:52:29 | INFO     | src.invoice_processor:__init__:78 - Invoice Processor initialized
2025-07-13 11:52:29 | INFO     | src.excel_manager:__init__:40 - Excel Manager initialized
2025-07-13 11:52:29 | INFO     | src.ai_manager:__init__:35 - AI Manager initialized with primary: openrouter
2025-07-13 11:52:29 | INFO     | src.ai_manager:__init__:36 - Available providers: ['deepseek', 'openrouter', 'ollama']
2025-07-13 11:52:29 | INFO     | src.invoice_processor:discover_invoice_files:275 - Discovered 4 invoice files in G:\vscodeapps\augment\invoicekeeper\data\invoices
2025-07-13 11:52:32 | INFO     | src.invoice_processor:process_batch:218 - Starting batch processing of 4 files
2025-07-13 11:52:32 | INFO     | src.invoice_processor:process_single_invoice:100 - Processing invoice: G:\vscodeapps\augment\invoicekeeper\data\invoices\Invoice-1.png
2025-07-13 11:52:33 | DEBUG    | src.utils:enhance_image_pil:104 - Enhanced image saved to: G:\vscodeapps\augment\invoicekeeper\data\processed\enhanced_Invoice-1.png
2025-07-13 11:52:33 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: openrouter
2025-07-13 11:52:33 | INFO     | src.invoice_processor:process_single_invoice:100 - Processing invoice: G:\vscodeapps\augment\invoicekeeper\data\invoices\Invoice-2.jpg
2025-07-13 11:52:34 | DEBUG    | src.utils:enhance_image_pil:104 - Enhanced image saved to: G:\vscodeapps\augment\invoicekeeper\data\processed\enhanced_Invoice-2.jpg
2025-07-13 11:52:34 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: openrouter
2025-07-13 11:52:34 | INFO     | src.invoice_processor:process_single_invoice:100 - Processing invoice: G:\vscodeapps\augment\invoicekeeper\data\invoices\Invoice-3.jpg
2025-07-13 11:52:35 | DEBUG    | src.utils:enhance_image_pil:104 - Enhanced image saved to: G:\vscodeapps\augment\invoicekeeper\data\processed\enhanced_Invoice-3.jpg
2025-07-13 11:52:35 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: openrouter
2025-07-13 11:52:35 | INFO     | src.invoice_processor:process_single_invoice:100 - Processing invoice: G:\vscodeapps\augment\invoicekeeper\data\invoices\Invoice-4.jpg
2025-07-13 11:52:36 | DEBUG    | src.utils:enhance_image_pil:104 - Enhanced image saved to: G:\vscodeapps\augment\invoicekeeper\data\processed\enhanced_Invoice-4.jpg
2025-07-13 11:52:36 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: openrouter
2025-07-13 11:52:38 | ERROR    | src.ai_manager:process_invoice:83 - Error with provider openrouter: OpenRouter API error: 404 - {"error":{"message":"No endpoints found for qwen/qwen-2-vl-7b-instruct:free.","code":404},"user_id":"user_2tx8armN8JKxx168S42Ir7XSphz"}
2025-07-13 11:52:38 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: deepseek
2025-07-13 11:52:40 | ERROR    | src.ai_manager:process_invoice:83 - Error with provider openrouter: OpenRouter API error: 404 - {"error":{"message":"No endpoints found for qwen/qwen-2-vl-7b-instruct:free.","code":404},"user_id":"user_2tx8armN8JKxx168S42Ir7XSphz"}
2025-07-13 11:52:40 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: deepseek
2025-07-13 11:52:42 | ERROR    | src.ai_manager:process_invoice:83 - Error with provider openrouter: OpenRouter API error: 404 - {"error":{"message":"No endpoints found for qwen/qwen-2-vl-7b-instruct:free.","code":404},"user_id":"user_2tx8armN8JKxx168S42Ir7XSphz"}
2025-07-13 11:52:42 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: deepseek
2025-07-13 11:52:44 | ERROR    | src.ai_manager:process_invoice:83 - Error with provider openrouter: OpenRouter API error: 404 - {"error":{"message":"No endpoints found for qwen/qwen-2-vl-7b-instruct:free.","code":404},"user_id":"user_2tx8armN8JKxx168S42Ir7XSphz"}
2025-07-13 11:52:44 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: deepseek
2025-07-13 11:52:58 | SUCCESS  | src.ai_manager:process_invoice:76 - Successfully processed with deepseek
2025-07-13 11:52:58 | SUCCESS  | src.invoice_processor:process_single_invoice:173 - Successfully processed G:\vscodeapps\augment\invoicekeeper\data\invoices\Invoice-3.jpg in 23.93s
2025-07-13 11:52:59 | SUCCESS  | src.ai_manager:process_invoice:76 - Successfully processed with deepseek
2025-07-13 11:52:59 | SUCCESS  | src.invoice_processor:process_single_invoice:173 - Successfully processed G:\vscodeapps\augment\invoicekeeper\data\invoices\Invoice-4.jpg in 23.19s
2025-07-13 11:52:59 | SUCCESS  | src.ai_manager:process_invoice:76 - Successfully processed with deepseek
2025-07-13 11:52:59 | SUCCESS  | src.invoice_processor:process_single_invoice:173 - Successfully processed G:\vscodeapps\augment\invoicekeeper\data\invoices\Invoice-1.png in 26.73s
2025-07-13 11:53:10 | SUCCESS  | src.ai_manager:process_invoice:76 - Successfully processed with deepseek
2025-07-13 11:53:10 | SUCCESS  | src.invoice_processor:process_single_invoice:173 - Successfully processed G:\vscodeapps\augment\invoicekeeper\data\invoices\Invoice-2.jpg in 37.38s
2025-07-13 11:53:10 | INFO     | src.invoice_processor:process_batch:245 - Batch processing completed: 4/4 successful
2025-07-13 11:53:10 | INFO     | src.excel_manager:_remove_duplicate_invoices:570 - Removed 1 duplicate entries
2025-07-13 11:53:10 | INFO     | src.excel_manager:_remove_duplicate_invoices:570 - Removed 10 duplicate entries
2025-07-13 11:53:10 | SUCCESS  | src.excel_manager:save_invoice_data:82 - Invoice data saved to: G:\vscodeapps\augment\invoicekeeper\data\output\Invoices_2025.xlsx
2025-07-13 12:00:54 | INFO     | src.logging_config:setup_logging:73 - Logging system initialized
2025-07-13 12:00:55 | INFO     | src.ai_manager:__init__:35 - AI Manager initialized with primary: openrouter
2025-07-13 12:00:55 | INFO     | src.ai_manager:__init__:36 - Available providers: ['deepseek', 'openrouter', 'ollama']
2025-07-13 12:00:55 | INFO     | src.invoice_processor:__init__:78 - Invoice Processor initialized
2025-07-13 12:00:55 | INFO     | src.excel_manager:__init__:40 - Excel Manager initialized
2025-07-13 12:00:55 | INFO     | src.ai_manager:__init__:35 - AI Manager initialized with primary: openrouter
2025-07-13 12:00:55 | INFO     | src.ai_manager:__init__:36 - Available providers: ['deepseek', 'openrouter', 'ollama']
2025-07-13 12:00:55 | INFO     | src.invoice_processor:discover_invoice_files:275 - Discovered 5 invoice files in G:\vscodeapps\augment\invoicekeeper\data\invoices
2025-07-13 12:00:58 | INFO     | src.invoice_processor:process_batch:218 - Starting batch processing of 5 files
2025-07-13 12:00:58 | INFO     | src.invoice_processor:process_single_invoice:100 - Processing invoice: G:\vscodeapps\augment\invoicekeeper\data\invoices\Invoice-1.png
2025-07-13 12:00:59 | DEBUG    | src.utils:enhance_image_pil:104 - Enhanced image saved to: G:\vscodeapps\augment\invoicekeeper\data\processed\enhanced_Invoice-1.png
2025-07-13 12:00:59 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: openrouter
2025-07-13 12:00:59 | INFO     | src.invoice_processor:process_single_invoice:100 - Processing invoice: G:\vscodeapps\augment\invoicekeeper\data\invoices\Invoice-2.jpg
2025-07-13 12:01:00 | DEBUG    | src.utils:enhance_image_pil:104 - Enhanced image saved to: G:\vscodeapps\augment\invoicekeeper\data\processed\enhanced_Invoice-2.jpg
2025-07-13 12:01:00 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: openrouter
2025-07-13 12:01:00 | INFO     | src.invoice_processor:process_single_invoice:100 - Processing invoice: G:\vscodeapps\augment\invoicekeeper\data\invoices\Invoice-3.jpg
2025-07-13 12:01:01 | DEBUG    | src.utils:enhance_image_pil:104 - Enhanced image saved to: G:\vscodeapps\augment\invoicekeeper\data\processed\enhanced_Invoice-3.jpg
2025-07-13 12:01:01 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: openrouter
2025-07-13 12:01:01 | INFO     | src.invoice_processor:process_single_invoice:100 - Processing invoice: G:\vscodeapps\augment\invoicekeeper\data\invoices\Invoice-4.jpg
2025-07-13 12:01:02 | DEBUG    | src.utils:enhance_image_pil:104 - Enhanced image saved to: G:\vscodeapps\augment\invoicekeeper\data\processed\enhanced_Invoice-4.jpg
2025-07-13 12:01:02 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: openrouter
2025-07-13 12:01:02 | INFO     | src.invoice_processor:process_single_invoice:100 - Processing invoice: G:\vscodeapps\augment\invoicekeeper\data\invoices\Invoice-5.jpg
2025-07-13 12:01:03 | DEBUG    | src.utils:enhance_image_pil:104 - Enhanced image saved to: G:\vscodeapps\augment\invoicekeeper\data\processed\enhanced_Invoice-5.jpg
2025-07-13 12:01:03 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: openrouter
2025-07-13 12:01:05 | ERROR    | src.ai_manager:process_invoice:83 - Error with provider openrouter: OpenRouter API error: 404 - {"error":{"message":"No endpoints found for qwen/qwen-2-vl-7b-instruct:free.","code":404},"user_id":"user_2tx8armN8JKxx168S42Ir7XSphz"}
2025-07-13 12:01:05 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: deepseek
2025-07-13 12:01:07 | ERROR    | src.ai_manager:process_invoice:83 - Error with provider openrouter: OpenRouter API error: 404 - {"error":{"message":"No endpoints found for qwen/qwen-2-vl-7b-instruct:free.","code":404},"user_id":"user_2tx8armN8JKxx168S42Ir7XSphz"}
2025-07-13 12:01:07 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: deepseek
2025-07-13 12:01:09 | ERROR    | src.ai_manager:process_invoice:83 - Error with provider openrouter: OpenRouter API error: 404 - {"error":{"message":"No endpoints found for qwen/qwen-2-vl-7b-instruct:free.","code":404},"user_id":"user_2tx8armN8JKxx168S42Ir7XSphz"}
2025-07-13 12:01:09 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: deepseek
2025-07-13 12:01:12 | ERROR    | src.ai_manager:process_invoice:83 - Error with provider openrouter: OpenRouter API error: 404 - {"error":{"message":"No endpoints found for qwen/qwen-2-vl-7b-instruct:free.","code":404},"user_id":"user_2tx8armN8JKxx168S42Ir7XSphz"}
2025-07-13 12:01:12 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: deepseek
2025-07-13 12:01:14 | ERROR    | src.ai_manager:process_invoice:83 - Error with provider openrouter: OpenRouter API error: 404 - {"error":{"message":"No endpoints found for qwen/qwen-2-vl-7b-instruct:free.","code":404},"user_id":"user_2tx8armN8JKxx168S42Ir7XSphz"}
2025-07-13 12:01:14 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: deepseek
2025-07-13 12:01:25 | SUCCESS  | src.ai_manager:process_invoice:76 - Successfully processed with deepseek
2025-07-13 12:01:25 | SUCCESS  | src.invoice_processor:process_single_invoice:173 - Successfully processed G:\vscodeapps\augment\invoicekeeper\data\invoices\Invoice-1.png in 27.16s
2025-07-13 12:01:27 | SUCCESS  | src.ai_manager:process_invoice:76 - Successfully processed with deepseek
2025-07-13 12:01:27 | SUCCESS  | src.invoice_processor:process_single_invoice:173 - Successfully processed G:\vscodeapps\augment\invoicekeeper\data\invoices\Invoice-3.jpg in 27.13s
2025-07-13 12:01:28 | SUCCESS  | src.ai_manager:process_invoice:76 - Successfully processed with deepseek
2025-07-13 12:01:28 | SUCCESS  | src.invoice_processor:process_single_invoice:173 - Successfully processed G:\vscodeapps\augment\invoicekeeper\data\invoices\Invoice-4.jpg in 26.73s
2025-07-13 12:01:32 | SUCCESS  | src.ai_manager:process_invoice:76 - Successfully processed with deepseek
2025-07-13 12:01:32 | SUCCESS  | src.invoice_processor:process_single_invoice:173 - Successfully processed G:\vscodeapps\augment\invoicekeeper\data\invoices\Invoice-5.jpg in 29.54s
2025-07-13 12:01:41 | SUCCESS  | src.ai_manager:process_invoice:76 - Successfully processed with deepseek
2025-07-13 12:01:41 | SUCCESS  | src.invoice_processor:process_single_invoice:173 - Successfully processed G:\vscodeapps\augment\invoicekeeper\data\invoices\Invoice-2.jpg in 41.90s
2025-07-13 12:01:41 | INFO     | src.invoice_processor:process_batch:245 - Batch processing completed: 5/5 successful
2025-07-13 12:01:41 | INFO     | src.excel_manager:_remove_duplicate_invoices:570 - Removed 1 duplicate entries
2025-07-13 12:01:41 | INFO     | src.excel_manager:_remove_duplicate_invoices:570 - Removed 15 duplicate entries
2025-07-13 12:01:41 | SUCCESS  | src.excel_manager:save_invoice_data:82 - Invoice data saved to: G:\vscodeapps\augment\invoicekeeper\data\output\Invoices_2025.xlsx
2025-07-13 12:06:22 | INFO     | src.logging_config:setup_logging:73 - Logging system initialized
2025-07-13 12:06:23 | INFO     | src.ai_manager:__init__:35 - AI Manager initialized with primary: openrouter
2025-07-13 12:06:23 | INFO     | src.ai_manager:__init__:36 - Available providers: ['deepseek', 'openrouter', 'ollama']
2025-07-13 12:06:23 | INFO     | src.invoice_processor:__init__:78 - Invoice Processor initialized
2025-07-13 12:06:23 | INFO     | src.excel_manager:__init__:40 - Excel Manager initialized
2025-07-13 12:06:23 | INFO     | src.ai_manager:__init__:35 - AI Manager initialized with primary: openrouter
2025-07-13 12:06:23 | INFO     | src.ai_manager:__init__:36 - Available providers: ['deepseek', 'openrouter', 'ollama']
2025-07-13 12:06:23 | INFO     | src.invoice_processor:discover_invoice_files:275 - Discovered 5 invoice files in G:\vscodeapps\augment\invoicekeeper\data\invoices
2025-07-13 12:06:25 | INFO     | src.invoice_processor:process_batch:218 - Starting batch processing of 5 files
2025-07-13 12:06:25 | INFO     | src.invoice_processor:process_single_invoice:100 - Processing invoice: G:\vscodeapps\augment\invoicekeeper\data\invoices\Invoice-1.png
2025-07-13 12:06:26 | DEBUG    | src.utils:enhance_image_pil:104 - Enhanced image saved to: G:\vscodeapps\augment\invoicekeeper\data\processed\enhanced_Invoice-1.png
2025-07-13 12:06:26 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: openrouter
2025-07-13 12:06:26 | INFO     | src.invoice_processor:process_single_invoice:100 - Processing invoice: G:\vscodeapps\augment\invoicekeeper\data\invoices\Invoice-2.jpg
2025-07-13 12:06:27 | DEBUG    | src.utils:enhance_image_pil:104 - Enhanced image saved to: G:\vscodeapps\augment\invoicekeeper\data\processed\enhanced_Invoice-2.jpg
2025-07-13 12:06:27 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: openrouter
2025-07-13 12:06:27 | INFO     | src.invoice_processor:process_single_invoice:100 - Processing invoice: G:\vscodeapps\augment\invoicekeeper\data\invoices\Invoice-3.jpg
2025-07-13 12:06:28 | DEBUG    | src.utils:enhance_image_pil:104 - Enhanced image saved to: G:\vscodeapps\augment\invoicekeeper\data\processed\enhanced_Invoice-3.jpg
2025-07-13 12:06:28 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: openrouter
2025-07-13 12:06:28 | INFO     | src.invoice_processor:process_single_invoice:100 - Processing invoice: G:\vscodeapps\augment\invoicekeeper\data\invoices\Invoice-4.jpg
2025-07-13 12:06:29 | DEBUG    | src.utils:enhance_image_pil:104 - Enhanced image saved to: G:\vscodeapps\augment\invoicekeeper\data\processed\enhanced_Invoice-4.jpg
2025-07-13 12:06:29 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: openrouter
2025-07-13 12:06:30 | INFO     | src.invoice_processor:process_single_invoice:100 - Processing invoice: G:\vscodeapps\augment\invoicekeeper\data\invoices\Invoice-5.jpg
2025-07-13 12:06:30 | DEBUG    | src.utils:enhance_image_pil:104 - Enhanced image saved to: G:\vscodeapps\augment\invoicekeeper\data\processed\enhanced_Invoice-5.jpg
2025-07-13 12:06:30 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: openrouter
2025-07-13 12:06:32 | ERROR    | src.ai_manager:process_invoice:83 - Error with provider openrouter: OpenRouter API error: 404 - {"error":{"message":"No endpoints found for qwen/qwen-2-vl-7b-instruct:free.","code":404},"user_id":"user_2tx8armN8JKxx168S42Ir7XSphz"}
2025-07-13 12:06:32 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: deepseek
2025-07-13 12:06:34 | ERROR    | src.ai_manager:process_invoice:83 - Error with provider openrouter: OpenRouter API error: 404 - {"error":{"message":"No endpoints found for qwen/qwen-2-vl-7b-instruct:free.","code":404},"user_id":"user_2tx8armN8JKxx168S42Ir7XSphz"}
2025-07-13 12:06:34 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: deepseek
2025-07-13 12:06:37 | ERROR    | src.ai_manager:process_invoice:83 - Error with provider openrouter: OpenRouter API error: 404 - {"error":{"message":"No endpoints found for qwen/qwen-2-vl-7b-instruct:free.","code":404},"user_id":"user_2tx8armN8JKxx168S42Ir7XSphz"}
2025-07-13 12:06:37 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: deepseek
2025-07-13 12:06:39 | ERROR    | src.ai_manager:process_invoice:83 - Error with provider openrouter: OpenRouter API error: 404 - {"error":{"message":"No endpoints found for qwen/qwen-2-vl-7b-instruct:free.","code":404},"user_id":"user_2tx8armN8JKxx168S42Ir7XSphz"}
2025-07-13 12:06:39 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: deepseek
2025-07-13 12:06:41 | ERROR    | src.ai_manager:process_invoice:83 - Error with provider openrouter: OpenRouter API error: 404 - {"error":{"message":"No endpoints found for qwen/qwen-2-vl-7b-instruct:free.","code":404},"user_id":"user_2tx8armN8JKxx168S42Ir7XSphz"}
2025-07-13 12:06:41 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: deepseek
2025-07-13 12:06:52 | SUCCESS  | src.ai_manager:process_invoice:76 - Successfully processed with deepseek
2025-07-13 12:06:52 | SUCCESS  | src.invoice_processor:process_single_invoice:173 - Successfully processed G:\vscodeapps\augment\invoicekeeper\data\invoices\Invoice-3.jpg in 24.85s
2025-07-13 12:06:53 | SUCCESS  | src.ai_manager:process_invoice:76 - Successfully processed with deepseek
2025-07-13 12:06:53 | SUCCESS  | src.invoice_processor:process_single_invoice:173 - Successfully processed G:\vscodeapps\augment\invoicekeeper\data\invoices\Invoice-1.png in 27.76s
2025-07-13 12:06:55 | SUCCESS  | src.ai_manager:process_invoice:76 - Successfully processed with deepseek
2025-07-13 12:06:55 | SUCCESS  | src.invoice_processor:process_single_invoice:173 - Successfully processed G:\vscodeapps\augment\invoicekeeper\data\invoices\Invoice-4.jpg in 26.88s
2025-07-13 12:07:08 | SUCCESS  | src.ai_manager:process_invoice:76 - Successfully processed with deepseek
2025-07-13 12:07:08 | SUCCESS  | src.invoice_processor:process_single_invoice:173 - Successfully processed G:\vscodeapps\augment\invoicekeeper\data\invoices\Invoice-5.jpg in 38.32s
2025-07-13 12:07:09 | SUCCESS  | src.ai_manager:process_invoice:76 - Successfully processed with deepseek
2025-07-13 12:07:09 | SUCCESS  | src.invoice_processor:process_single_invoice:173 - Successfully processed G:\vscodeapps\augment\invoicekeeper\data\invoices\Invoice-2.jpg in 42.44s
2025-07-13 12:07:09 | INFO     | src.invoice_processor:process_batch:245 - Batch processing completed: 5/5 successful
2025-07-13 12:07:09 | SUCCESS  | src.excel_manager:save_invoice_data:82 - Invoice data saved to: G:\vscodeapps\augment\invoicekeeper\data\output\Invoices_2025.xlsx
2025-07-13 12:23:37 | INFO     | src.logging_config:setup_logging:73 - Logging system initialized
2025-07-13 12:23:38 | INFO     | src.ai_manager:__init__:35 - AI Manager initialized with primary: openrouter
2025-07-13 12:23:38 | INFO     | src.ai_manager:__init__:36 - Available providers: ['deepseek', 'openrouter', 'ollama']
2025-07-13 12:23:38 | INFO     | src.invoice_processor:__init__:78 - Invoice Processor initialized
2025-07-13 12:23:38 | INFO     | src.excel_manager:__init__:40 - Excel Manager initialized
2025-07-13 12:23:38 | INFO     | src.ai_manager:__init__:35 - AI Manager initialized with primary: openrouter
2025-07-13 12:23:38 | INFO     | src.ai_manager:__init__:36 - Available providers: ['deepseek', 'openrouter', 'ollama']
2025-07-13 12:23:38 | INFO     | src.invoice_processor:discover_invoice_files:275 - Discovered 1 invoice files in G:\vscodeapps\augment\invoicekeeper\data\invoices
2025-07-13 12:23:41 | INFO     | src.invoice_processor:process_batch:218 - Starting batch processing of 1 files
2025-07-13 12:23:41 | INFO     | src.invoice_processor:process_single_invoice:100 - Processing invoice: G:\vscodeapps\augment\invoicekeeper\data\invoices\Temu.png
2025-07-13 12:23:41 | DEBUG    | src.utils:enhance_image_pil:104 - Enhanced image saved to: G:\vscodeapps\augment\invoicekeeper\data\processed\enhanced_Temu.png
2025-07-13 12:23:41 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: openrouter
2025-07-13 12:23:44 | ERROR    | src.ai_manager:process_invoice:83 - Error with provider openrouter: OpenRouter API error: 404 - {"error":{"message":"No endpoints found for qwen/qwen-2-vl-7b-instruct:free.","code":404},"user_id":"user_2tx8armN8JKxx168S42Ir7XSphz"}
2025-07-13 12:23:44 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: deepseek
2025-07-13 12:24:06 | SUCCESS  | src.ai_manager:process_invoice:76 - Successfully processed with deepseek
2025-07-13 12:24:06 | ERROR    | src.invoice_processor:process_single_invoice:192 - Failed to process G:\vscodeapps\augment\invoicekeeper\data\invoices\Temu.png: float() argument must be a string or a real number, not 'NoneType'
2025-07-13 12:24:06 | INFO     | src.invoice_processor:process_batch:245 - Batch processing completed: 0/1 successful
2025-07-13 13:09:31 | INFO     | src.logging_config:setup_logging:73 - Logging system initialized
2025-07-13 13:09:31 | INFO     | src.ai_manager:__init__:35 - AI Manager initialized with primary: openrouter
2025-07-13 13:09:31 | INFO     | src.ai_manager:__init__:36 - Available providers: ['deepseek', 'openrouter', 'ollama']
2025-07-13 13:09:31 | INFO     | src.invoice_processor:__init__:78 - Invoice Processor initialized
2025-07-13 13:09:31 | INFO     | src.excel_manager:__init__:40 - Excel Manager initialized
2025-07-13 13:09:32 | INFO     | src.ai_manager:__init__:35 - AI Manager initialized with primary: openrouter
2025-07-13 13:09:32 | INFO     | src.ai_manager:__init__:36 - Available providers: ['deepseek', 'openrouter', 'ollama']
2025-07-13 13:09:32 | INFO     | src.invoice_processor:discover_invoice_files:289 - Discovered 1 invoice files in data\invoices
2025-07-13 13:09:38 | INFO     | src.invoice_processor:process_batch:232 - Starting batch processing of 1 files
2025-07-13 13:09:38 | INFO     | src.invoice_processor:process_single_invoice:100 - Processing invoice: data\invoices\Temu.png
2025-07-13 13:09:39 | DEBUG    | src.utils:enhance_image_pil:104 - Enhanced image saved to: G:\vscodeapps\augment\invoicekeeper\data\processed\enhanced_Temu.png
2025-07-13 13:09:39 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: openrouter
2025-07-13 13:09:48 | SUCCESS  | src.ai_manager:process_invoice:76 - Successfully processed with openrouter
2025-07-13 13:09:48 | ERROR    | src.invoice_processor:process_single_invoice:201 - Failed to process data\invoices\Temu.png: float() argument must be a string or a real number, not 'NoneType'
2025-07-13 13:09:48 | DEBUG    | src.invoice_processor:move_file_to_failed:354 - Moved failed file to: G:\vscodeapps\augment\invoicekeeper\data\failed\2025\07\Temu.png
2025-07-13 13:09:48 | INFO     | src.invoice_processor:process_single_invoice:206 - Moved failed file to: G:\vscodeapps\augment\invoicekeeper\data\failed\2025\07\Temu.png
2025-07-13 13:09:48 | INFO     | src.invoice_processor:process_batch:259 - Batch processing completed: 0/1 successful
2025-07-13 13:12:04 | INFO     | src.logging_config:setup_logging:73 - Logging system initialized
2025-07-13 13:12:05 | INFO     | src.ai_manager:__init__:35 - AI Manager initialized with primary: openrouter
2025-07-13 13:12:05 | INFO     | src.ai_manager:__init__:36 - Available providers: ['deepseek', 'openrouter', 'ollama']
2025-07-13 13:12:05 | INFO     | src.invoice_processor:__init__:78 - Invoice Processor initialized
2025-07-13 13:12:05 | INFO     | src.excel_manager:__init__:40 - Excel Manager initialized
2025-07-13 13:12:05 | INFO     | src.ai_manager:__init__:35 - AI Manager initialized with primary: openrouter
2025-07-13 13:12:05 | INFO     | src.ai_manager:__init__:36 - Available providers: ['deepseek', 'openrouter', 'ollama']
2025-07-13 13:12:05 | INFO     | src.invoice_processor:discover_invoice_files:289 - Discovered 1 invoice files in data\invoices
2025-07-13 13:12:10 | INFO     | src.invoice_processor:process_batch:232 - Starting batch processing of 1 files
2025-07-13 13:12:10 | INFO     | src.invoice_processor:process_single_invoice:100 - Processing invoice: data\invoices\Temu.png
2025-07-13 13:12:10 | DEBUG    | src.utils:enhance_image_pil:104 - Enhanced image saved to: G:\vscodeapps\augment\invoicekeeper\data\processed\enhanced_Temu.png
2025-07-13 13:12:10 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: deepseek
2025-07-13 13:12:32 | SUCCESS  | src.ai_manager:process_invoice:76 - Successfully processed with deepseek
2025-07-13 13:12:32 | SUCCESS  | src.invoice_processor:process_single_invoice:177 - Successfully processed data\invoices\Temu.png in 22.34s
2025-07-13 13:12:32 | DEBUG    | src.invoice_processor:move_file_to_processed:319 - Moved successful file to: G:\vscodeapps\augment\invoicekeeper\data\processed\2025\07\Temu.png
2025-07-13 13:12:32 | INFO     | src.invoice_processor:process_single_invoice:182 - Moved successful file to: G:\vscodeapps\augment\invoicekeeper\data\processed\2025\07\Temu.png
2025-07-13 13:12:32 | INFO     | src.invoice_processor:process_batch:259 - Batch processing completed: 1/1 successful
2025-07-13 13:12:32 | INFO     | src.excel_manager:_remove_duplicate_invoices:570 - Removed 1 duplicate entries
2025-07-13 13:12:32 | SUCCESS  | src.excel_manager:save_invoice_data:82 - Invoice data saved to: G:\vscodeapps\augment\invoicekeeper\data\output\Invoices_2025.xlsx
2025-07-13 13:48:26 | INFO     | src.logging_config:setup_logging:73 - Logging system initialized
2025-07-13 13:48:27 | INFO     | src.ai_manager:__init__:35 - AI Manager initialized with primary: openrouter
2025-07-13 13:48:27 | INFO     | src.ai_manager:__init__:36 - Available providers: ['deepseek', 'openrouter', 'ollama']
2025-07-13 13:48:27 | INFO     | src.invoice_processor:__init__:78 - Invoice Processor initialized
2025-07-13 13:48:27 | INFO     | src.excel_manager:__init__:40 - Excel Manager initialized
2025-07-13 13:48:27 | INFO     | src.ai_manager:__init__:35 - AI Manager initialized with primary: openrouter
2025-07-13 13:48:27 | INFO     | src.ai_manager:__init__:36 - Available providers: ['deepseek', 'openrouter', 'ollama']
2025-07-13 13:48:27 | INFO     | src.invoice_processor:discover_invoice_files:289 - Discovered 3 invoice files in data\invoices
2025-07-13 13:48:30 | INFO     | src.invoice_processor:process_batch:232 - Starting batch processing of 3 files
2025-07-13 13:48:30 | INFO     | src.invoice_processor:process_single_invoice:100 - Processing invoice: data\invoices\Invoice-1.png
2025-07-13 13:48:30 | DEBUG    | src.utils:enhance_image_pil:104 - Enhanced image saved to: G:\vscodeapps\augment\invoicekeeper\data\processed\enhanced_Invoice-1.png
2025-07-13 13:48:30 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: deepseek
2025-07-13 13:48:32 | INFO     | src.invoice_processor:process_single_invoice:100 - Processing invoice: data\invoices\Invoice-2.jpg
2025-07-13 13:48:33 | DEBUG    | src.utils:enhance_image_pil:104 - Enhanced image saved to: G:\vscodeapps\augment\invoicekeeper\data\processed\enhanced_Invoice-2.jpg
2025-07-13 13:48:33 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: deepseek
2025-07-13 13:48:36 | INFO     | src.invoice_processor:process_single_invoice:100 - Processing invoice: data\invoices\Temu.png
2025-07-13 13:48:36 | DEBUG    | src.utils:enhance_image_pil:104 - Enhanced image saved to: G:\vscodeapps\augment\invoicekeeper\data\processed\enhanced_Temu.png
2025-07-13 13:48:36 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: deepseek
2025-07-13 13:48:47 | SUCCESS  | src.ai_manager:process_invoice:76 - Successfully processed with deepseek
2025-07-13 13:48:47 | SUCCESS  | src.invoice_processor:process_single_invoice:177 - Successfully processed data\invoices\Invoice-1.png in 17.10s
2025-07-13 13:48:47 | DEBUG    | src.invoice_processor:move_file_to_processed:319 - Moved successful file to: G:\vscodeapps\augment\invoicekeeper\data\processed\2025\07\Invoice-1.png
2025-07-13 13:48:47 | INFO     | src.invoice_processor:process_single_invoice:182 - Moved successful file to: G:\vscodeapps\augment\invoicekeeper\data\processed\2025\07\Invoice-1.png
2025-07-13 13:49:00 | SUCCESS  | src.ai_manager:process_invoice:76 - Successfully processed with deepseek
2025-07-13 13:49:00 | SUCCESS  | src.invoice_processor:process_single_invoice:177 - Successfully processed data\invoices\Temu.png in 24.13s
2025-07-13 13:49:00 | DEBUG    | src.invoice_processor:move_file_to_processed:319 - Moved successful file to: G:\vscodeapps\augment\invoicekeeper\data\processed\2025\07\Temu.png
2025-07-13 13:49:00 | INFO     | src.invoice_processor:process_single_invoice:182 - Moved successful file to: G:\vscodeapps\augment\invoicekeeper\data\processed\2025\07\Temu.png
2025-07-13 13:49:03 | SUCCESS  | src.ai_manager:process_invoice:76 - Successfully processed with deepseek
2025-07-13 13:49:03 | SUCCESS  | src.invoice_processor:process_single_invoice:177 - Successfully processed data\invoices\Invoice-2.jpg in 30.84s
2025-07-13 13:49:03 | DEBUG    | src.invoice_processor:move_file_to_processed:319 - Moved successful file to: G:\vscodeapps\augment\invoicekeeper\data\processed\2025\07\Invoice-2.jpg
2025-07-13 13:49:03 | INFO     | src.invoice_processor:process_single_invoice:182 - Moved successful file to: G:\vscodeapps\augment\invoicekeeper\data\processed\2025\07\Invoice-2.jpg
2025-07-13 13:49:03 | INFO     | src.invoice_processor:process_batch:259 - Batch processing completed: 3/3 successful
2025-07-13 13:49:03 | SUCCESS  | src.excel_manager:save_invoice_data:82 - Invoice data saved to: G:\vscodeapps\augment\invoicekeeper\data\output\Invoices_2025.xlsx
2025-07-13 13:59:46 | INFO     | src.logging_config:setup_logging:73 - Logging system initialized
2025-07-13 13:59:47 | INFO     | src.ai_manager:__init__:35 - AI Manager initialized with primary: openrouter
2025-07-13 13:59:47 | INFO     | src.ai_manager:__init__:36 - Available providers: ['deepseek', 'openrouter', 'ollama']
2025-07-13 13:59:47 | INFO     | src.invoice_processor:__init__:78 - Invoice Processor initialized
2025-07-13 13:59:47 | INFO     | src.excel_manager:__init__:40 - Excel Manager initialized
2025-07-13 13:59:47 | INFO     | src.ai_manager:__init__:35 - AI Manager initialized with primary: openrouter
2025-07-13 13:59:47 | INFO     | src.ai_manager:__init__:36 - Available providers: ['deepseek', 'openrouter', 'ollama']
2025-07-13 13:59:47 | INFO     | src.invoice_processor:discover_invoice_files:289 - Discovered 3 invoice files in data\invoices
2025-07-13 13:59:50 | INFO     | src.invoice_processor:process_batch:232 - Starting batch processing of 3 files
2025-07-13 13:59:50 | INFO     | src.invoice_processor:process_single_invoice:100 - Processing invoice: data\invoices\Invoice-3.jpg
2025-07-13 13:59:50 | DEBUG    | src.utils:enhance_image_pil:104 - Enhanced image saved to: G:\vscodeapps\augment\invoicekeeper\data\processed\enhanced_Invoice-3.jpg
2025-07-13 13:59:50 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: deepseek
2025-07-13 13:59:53 | INFO     | src.invoice_processor:process_single_invoice:100 - Processing invoice: data\invoices\Invoice-4.jpg
2025-07-13 13:59:54 | DEBUG    | src.utils:enhance_image_pil:104 - Enhanced image saved to: G:\vscodeapps\augment\invoicekeeper\data\processed\enhanced_Invoice-4.jpg
2025-07-13 13:59:54 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: deepseek
2025-07-13 13:59:56 | INFO     | src.invoice_processor:process_single_invoice:100 - Processing invoice: data\invoices\Invoice-5.jpg
2025-07-13 13:59:57 | DEBUG    | src.utils:enhance_image_pil:104 - Enhanced image saved to: G:\vscodeapps\augment\invoicekeeper\data\processed\enhanced_Invoice-5.jpg
2025-07-13 13:59:57 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: deepseek
2025-07-13 14:00:11 | SUCCESS  | src.ai_manager:process_invoice:76 - Successfully processed with deepseek
2025-07-13 14:00:11 | SUCCESS  | src.invoice_processor:process_single_invoice:177 - Successfully processed data\invoices\Invoice-3.jpg in 21.12s
2025-07-13 14:00:11 | DEBUG    | src.invoice_processor:move_file_to_processed:319 - Moved successful file to: G:\vscodeapps\augment\invoicekeeper\data\processed\2025\07\Invoice-3.jpg
2025-07-13 14:00:11 | INFO     | src.invoice_processor:process_single_invoice:182 - Moved successful file to: G:\vscodeapps\augment\invoicekeeper\data\processed\2025\07\Invoice-3.jpg
2025-07-13 14:00:11 | SUCCESS  | src.ai_manager:process_invoice:76 - Successfully processed with deepseek
2025-07-13 14:00:11 | SUCCESS  | src.invoice_processor:process_single_invoice:177 - Successfully processed data\invoices\Invoice-4.jpg in 18.08s
2025-07-13 14:00:11 | DEBUG    | src.invoice_processor:move_file_to_processed:319 - Moved successful file to: G:\vscodeapps\augment\invoicekeeper\data\processed\2025\07\Invoice-4.jpg
2025-07-13 14:00:11 | INFO     | src.invoice_processor:process_single_invoice:182 - Moved successful file to: G:\vscodeapps\augment\invoicekeeper\data\processed\2025\07\Invoice-4.jpg
2025-07-13 14:00:25 | SUCCESS  | src.ai_manager:process_invoice:76 - Successfully processed with deepseek
2025-07-13 14:00:25 | SUCCESS  | src.invoice_processor:process_single_invoice:177 - Successfully processed data\invoices\Invoice-5.jpg in 29.03s
2025-07-13 14:00:25 | DEBUG    | src.invoice_processor:move_file_to_processed:319 - Moved successful file to: G:\vscodeapps\augment\invoicekeeper\data\processed\2025\07\Invoice-5.jpg
2025-07-13 14:00:25 | INFO     | src.invoice_processor:process_single_invoice:182 - Moved successful file to: G:\vscodeapps\augment\invoicekeeper\data\processed\2025\07\Invoice-5.jpg
2025-07-13 14:00:25 | INFO     | src.invoice_processor:process_batch:259 - Batch processing completed: 3/3 successful
2025-07-13 14:00:25 | INFO     | src.excel_manager:_remove_duplicate_invoices:641 - Removed 1 duplicate entries
2025-07-13 14:00:25 | SUCCESS  | src.excel_manager:save_invoice_data:82 - Invoice data saved to: G:\vscodeapps\augment\invoicekeeper\data\output\Invoices_2025.xlsx
2025-07-15 12:03:48 | INFO     | src.logging_config:setup_logging:73 - Logging system initialized
2025-07-15 12:04:15 | INFO     | src.logging_config:setup_logging:73 - Logging system initialized
2025-07-15 12:04:15 | INFO     | src.ai_manager:__init__:35 - AI Manager initialized with primary: openrouter
2025-07-15 12:04:15 | INFO     | src.ai_manager:__init__:36 - Available providers: ['deepseek', 'openrouter', 'ollama']
2025-07-15 12:04:15 | INFO     | src.invoice_processor:__init__:78 - Invoice Processor initialized
2025-07-15 12:04:15 | INFO     | src.excel_manager:__init__:40 - Excel Manager initialized
2025-07-15 12:04:16 | INFO     | src.ai_manager:__init__:35 - AI Manager initialized with primary: openrouter
2025-07-15 12:04:16 | INFO     | src.ai_manager:__init__:36 - Available providers: ['deepseek', 'openrouter', 'ollama']
2025-07-15 12:04:16 | INFO     | src.invoice_processor:discover_invoice_files:289 - Discovered 1 invoice files in G:\vscodeapps\augment\invoicekeeper\data\invoices
2025-07-15 12:04:20 | INFO     | src.invoice_processor:process_batch:232 - Starting batch processing of 1 files
2025-07-15 12:04:20 | INFO     | src.invoice_processor:process_single_invoice:100 - Processing invoice: G:\vscodeapps\augment\invoicekeeper\data\invoices\Screenshot 2025-07-15 120050.png
2025-07-15 12:04:20 | DEBUG    | src.utils:enhance_image_pil:104 - Enhanced image saved to: G:\vscodeapps\augment\invoicekeeper\data\processed\enhanced_Screenshot 2025-07-15 120050.png
2025-07-15 12:04:20 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: openrouter
2025-07-15 12:04:49 | ERROR    | src.ai_manager:process_invoice:83 - Error with provider openrouter: 'choices'
2025-07-15 12:04:49 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: deepseek
2025-07-15 12:05:02 | SUCCESS  | src.ai_manager:process_invoice:76 - Successfully processed with deepseek
2025-07-15 12:05:02 | SUCCESS  | src.invoice_processor:process_single_invoice:177 - Successfully processed G:\vscodeapps\augment\invoicekeeper\data\invoices\Screenshot 2025-07-15 120050.png in 42.22s
2025-07-15 12:05:02 | DEBUG    | src.invoice_processor:move_file_to_processed:319 - Moved successful file to: G:\vscodeapps\augment\invoicekeeper\data\processed\2025\07\Screenshot 2025-07-15 120050.png
2025-07-15 12:05:02 | INFO     | src.invoice_processor:process_single_invoice:182 - Moved successful file to: G:\vscodeapps\augment\invoicekeeper\data\processed\2025\07\Screenshot 2025-07-15 120050.png
2025-07-15 12:05:02 | INFO     | src.invoice_processor:process_batch:259 - Batch processing completed: 1/1 successful
2025-07-15 12:05:02 | INFO     | src.excel_manager:_remove_duplicate_invoices:641 - Removed 1 duplicate entries
2025-07-15 12:05:02 | SUCCESS  | src.excel_manager:save_invoice_data:82 - Invoice data saved to: G:\vscodeapps\augment\invoicekeeper\data\output\Invoices_2025.xlsx
2025-07-19 23:59:11 | INFO     | src.logging_config:setup_logging:73 - Logging system initialized
2025-07-19 23:59:11 | INFO     | src.ai_manager:__init__:35 - AI Manager initialized with primary: openrouter
2025-07-19 23:59:11 | INFO     | src.ai_manager:__init__:36 - Available providers: ['deepseek', 'openrouter', 'ollama']
2025-07-19 23:59:11 | INFO     | src.invoice_processor:__init__:78 - Invoice Processor initialized
2025-07-19 23:59:11 | INFO     | src.excel_manager:__init__:40 - Excel Manager initialized
2025-07-19 23:59:12 | INFO     | src.ai_manager:__init__:35 - AI Manager initialized with primary: openrouter
2025-07-19 23:59:12 | INFO     | src.ai_manager:__init__:36 - Available providers: ['deepseek', 'openrouter', 'ollama']
2025-07-19 23:59:12 | INFO     | src.invoice_processor:discover_invoice_files:289 - Discovered 0 invoice files in G:\vscodeapps\augment\invoicekeeper\data\invoices
2025-07-20 00:02:56 | INFO     | src.logging_config:setup_logging:73 - Logging system initialized
2025-07-20 00:02:57 | INFO     | src.ai_manager:__init__:35 - AI Manager initialized with primary: openrouter
2025-07-20 00:02:57 | INFO     | src.ai_manager:__init__:36 - Available providers: ['deepseek', 'openrouter', 'ollama']
2025-07-20 00:02:57 | INFO     | src.invoice_processor:__init__:78 - Invoice Processor initialized
2025-07-20 00:02:57 | INFO     | src.excel_manager:__init__:40 - Excel Manager initialized
2025-07-20 00:02:57 | INFO     | src.ai_manager:__init__:35 - AI Manager initialized with primary: openrouter
2025-07-20 00:02:57 | INFO     | src.ai_manager:__init__:36 - Available providers: ['deepseek', 'openrouter', 'ollama']
2025-07-20 00:02:57 | INFO     | src.invoice_processor:discover_invoice_files:289 - Discovered 1 invoice files in G:\vscodeapps\augment\invoicekeeper\data\invoices
2025-07-20 00:03:01 | INFO     | src.invoice_processor:process_batch:232 - Starting batch processing of 1 files
2025-07-20 00:03:01 | INFO     | src.invoice_processor:process_single_invoice:100 - Processing invoice: G:\vscodeapps\augment\invoicekeeper\data\invoices\20250715_183725.jpg
2025-07-20 00:03:02 | DEBUG    | src.utils:enhance_image_pil:104 - Enhanced image saved to: G:\vscodeapps\augment\invoicekeeper\data\processed\enhanced_20250715_183725.jpg
2025-07-20 00:03:02 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: openrouter
2025-07-20 00:03:20 | SUCCESS  | src.ai_manager:process_invoice:76 - Successfully processed with openrouter
2025-07-20 00:03:20 | SUCCESS  | src.invoice_processor:process_single_invoice:177 - Successfully processed G:\vscodeapps\augment\invoicekeeper\data\invoices\20250715_183725.jpg in 19.42s
2025-07-20 00:03:20 | DEBUG    | src.invoice_processor:move_file_to_processed:319 - Moved successful file to: G:\vscodeapps\augment\invoicekeeper\data\processed\2025\07\20250715_183725.jpg
2025-07-20 00:03:20 | INFO     | src.invoice_processor:process_single_invoice:182 - Moved successful file to: G:\vscodeapps\augment\invoicekeeper\data\processed\2025\07\20250715_183725.jpg
2025-07-20 00:03:20 | INFO     | src.invoice_processor:process_batch:259 - Batch processing completed: 1/1 successful
2025-07-20 00:03:20 | SUCCESS  | src.excel_manager:save_invoice_data:82 - Invoice data saved to: G:\vscodeapps\augment\invoicekeeper\data\output\Invoices_2025.xlsx
2025-08-10 16:10:26 | INFO     | src.logging_config:setup_logging:73 - Logging system initialized
2025-08-10 16:10:27 | INFO     | src.ai_manager:__init__:35 - AI Manager initialized with primary: openrouter
2025-08-10 16:10:27 | INFO     | src.ai_manager:__init__:36 - Available providers: ['deepseek', 'openrouter', 'ollama']
2025-08-10 16:10:27 | INFO     | src.invoice_processor:__init__:78 - Invoice Processor initialized
2025-08-10 16:10:27 | INFO     | src.excel_manager:__init__:40 - Excel Manager initialized
2025-08-10 16:10:27 | INFO     | src.ai_manager:__init__:35 - AI Manager initialized with primary: openrouter
2025-08-10 16:10:27 | INFO     | src.ai_manager:__init__:36 - Available providers: ['deepseek', 'openrouter', 'ollama']
2025-08-10 16:10:27 | INFO     | src.invoice_processor:discover_invoice_files:289 - Discovered 2 invoice files in G:\vscodeapps\augment\invoicekeeper\data\invoices
2025-08-10 16:10:31 | INFO     | src.invoice_processor:process_batch:232 - Starting batch processing of 2 files
2025-08-10 16:10:31 | INFO     | src.invoice_processor:process_single_invoice:100 - Processing invoice: G:\vscodeapps\augment\invoicekeeper\data\invoices\Calicut_20250810160800.jpg
2025-08-10 16:10:32 | DEBUG    | src.utils:enhance_image_pil:104 - Enhanced image saved to: G:\vscodeapps\augment\invoicekeeper\data\processed\enhanced_Calicut_20250810160800.jpg
2025-08-10 16:10:32 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: openrouter
2025-08-10 16:10:32 | INFO     | src.invoice_processor:process_single_invoice:100 - Processing invoice: G:\vscodeapps\augment\invoicekeeper\data\invoices\VIVA_20250810160802.jpg
2025-08-10 16:10:32 | DEBUG    | src.utils:enhance_image_pil:104 - Enhanced image saved to: G:\vscodeapps\augment\invoicekeeper\data\processed\enhanced_VIVA_20250810160802.jpg
2025-08-10 16:10:33 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: openrouter
2025-08-10 16:10:42 | SUCCESS  | src.ai_manager:process_invoice:76 - Successfully processed with openrouter
2025-08-10 16:10:42 | SUCCESS  | src.invoice_processor:process_single_invoice:177 - Successfully processed G:\vscodeapps\augment\invoicekeeper\data\invoices\Calicut_20250810160800.jpg in 10.52s
2025-08-10 16:10:42 | DEBUG    | src.invoice_processor:move_file_to_processed:319 - Moved successful file to: G:\vscodeapps\augment\invoicekeeper\data\processed\2025\08\Calicut_20250810160800.jpg
2025-08-10 16:10:42 | INFO     | src.invoice_processor:process_single_invoice:182 - Moved successful file to: G:\vscodeapps\augment\invoicekeeper\data\processed\2025\08\Calicut_20250810160800.jpg
2025-08-10 16:10:43 | SUCCESS  | src.ai_manager:process_invoice:76 - Successfully processed with openrouter
2025-08-10 16:10:43 | SUCCESS  | src.invoice_processor:process_single_invoice:177 - Successfully processed G:\vscodeapps\augment\invoicekeeper\data\invoices\VIVA_20250810160802.jpg in 10.36s
2025-08-10 16:10:43 | DEBUG    | src.invoice_processor:move_file_to_processed:319 - Moved successful file to: G:\vscodeapps\augment\invoicekeeper\data\processed\2025\08\VIVA_20250810160802.jpg
2025-08-10 16:10:43 | INFO     | src.invoice_processor:process_single_invoice:182 - Moved successful file to: G:\vscodeapps\augment\invoicekeeper\data\processed\2025\08\VIVA_20250810160802.jpg
2025-08-10 16:10:43 | INFO     | src.invoice_processor:process_batch:259 - Batch processing completed: 2/2 successful
2025-08-10 16:10:43 | SUCCESS  | src.excel_manager:save_invoice_data:82 - Invoice data saved to: G:\vscodeapps\augment\invoicekeeper\data\output\Invoices_2025.xlsx
2025-08-14 13:05:51 | INFO     | src.logging_config:setup_logging:73 - Logging system initialized
2025-08-14 13:05:52 | INFO     | src.ai_manager:__init__:35 - AI Manager initialized with primary: openrouter
2025-08-14 13:05:52 | INFO     | src.ai_manager:__init__:36 - Available providers: ['deepseek', 'openrouter', 'ollama']
2025-08-14 13:05:52 | INFO     | src.invoice_processor:__init__:78 - Invoice Processor initialized
2025-08-14 13:05:52 | INFO     | src.excel_manager:__init__:40 - Excel Manager initialized
2025-08-14 13:05:52 | INFO     | src.ai_manager:__init__:35 - AI Manager initialized with primary: openrouter
2025-08-14 13:05:52 | INFO     | src.ai_manager:__init__:36 - Available providers: ['deepseek', 'openrouter', 'ollama']
2025-08-14 13:05:52 | INFO     | src.invoice_processor:discover_invoice_files:289 - Discovered 1 invoice files in G:\vscodeapps\augment\invoicekeeper\data\invoices
2025-08-14 13:05:55 | INFO     | src.invoice_processor:process_batch:232 - Starting batch processing of 1 files
2025-08-14 13:05:55 | INFO     | src.invoice_processor:process_single_invoice:100 - Processing invoice: G:\vscodeapps\augment\invoicekeeper\data\invoices\Invoice_2025-08-14_130400.jpg
2025-08-14 13:05:55 | DEBUG    | src.utils:enhance_image_pil:104 - Enhanced image saved to: G:\vscodeapps\augment\invoicekeeper\data\processed\enhanced_Invoice_2025-08-14_130400.jpg
2025-08-14 13:05:55 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: deepseek
2025-08-14 13:06:22 | SUCCESS  | src.ai_manager:process_invoice:76 - Successfully processed with deepseek
2025-08-14 13:06:22 | SUCCESS  | src.invoice_processor:process_single_invoice:177 - Successfully processed G:\vscodeapps\augment\invoicekeeper\data\invoices\Invoice_2025-08-14_130400.jpg in 27.09s
2025-08-14 13:06:22 | DEBUG    | src.invoice_processor:move_file_to_processed:319 - Moved successful file to: G:\vscodeapps\augment\invoicekeeper\data\processed\2025\08\Invoice_2025-08-14_130400.jpg
2025-08-14 13:06:22 | INFO     | src.invoice_processor:process_single_invoice:182 - Moved successful file to: G:\vscodeapps\augment\invoicekeeper\data\processed\2025\08\Invoice_2025-08-14_130400.jpg
2025-08-14 13:06:22 | INFO     | src.invoice_processor:process_batch:259 - Batch processing completed: 1/1 successful
2025-08-14 13:06:22 | SUCCESS  | src.excel_manager:save_invoice_data:82 - Invoice data saved to: G:\vscodeapps\augment\invoicekeeper\data\output\Invoices_2025.xlsx
2025-08-17 13:31:21 | INFO     | src.logging_config:setup_logging:73 - Logging system initialized
2025-08-17 13:31:22 | INFO     | src.ai_manager:__init__:35 - AI Manager initialized with primary: openrouter
2025-08-17 13:31:22 | INFO     | src.ai_manager:__init__:36 - Available providers: ['deepseek', 'openrouter', 'ollama']
2025-08-17 13:31:22 | INFO     | src.invoice_processor:__init__:78 - Invoice Processor initialized
2025-08-17 13:31:22 | INFO     | src.excel_manager:__init__:40 - Excel Manager initialized
2025-08-17 13:31:22 | INFO     | src.ai_manager:__init__:35 - AI Manager initialized with primary: openrouter
2025-08-17 13:31:22 | INFO     | src.ai_manager:__init__:36 - Available providers: ['deepseek', 'openrouter', 'ollama']
2025-08-17 13:31:22 | INFO     | src.invoice_processor:discover_invoice_files:289 - Discovered 1 invoice files in G:\vscodeapps\augment\invoicekeeper\data\invoices
2025-08-17 13:31:25 | INFO     | src.invoice_processor:process_batch:232 - Starting batch processing of 1 files
2025-08-17 13:31:25 | INFO     | src.invoice_processor:process_single_invoice:100 - Processing invoice: G:\vscodeapps\augment\invoicekeeper\data\invoices\DayToDAy_2025-08-17_132754.jpg
2025-08-17 13:31:25 | DEBUG    | src.utils:enhance_image_pil:104 - Enhanced image saved to: G:\vscodeapps\augment\invoicekeeper\data\processed\enhanced_DayToDAy_2025-08-17_132754.jpg
2025-08-17 13:31:25 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: openrouter
2025-08-17 13:31:34 | SUCCESS  | src.ai_manager:process_invoice:76 - Successfully processed with openrouter
2025-08-17 13:31:34 | SUCCESS  | src.invoice_processor:process_single_invoice:177 - Successfully processed G:\vscodeapps\augment\invoicekeeper\data\invoices\DayToDAy_2025-08-17_132754.jpg in 8.66s
2025-08-17 13:31:34 | DEBUG    | src.invoice_processor:move_file_to_processed:319 - Moved successful file to: G:\vscodeapps\augment\invoicekeeper\data\processed\2025\08\DayToDAy_2025-08-17_132754.jpg
2025-08-17 13:31:34 | INFO     | src.invoice_processor:process_single_invoice:182 - Moved successful file to: G:\vscodeapps\augment\invoicekeeper\data\processed\2025\08\DayToDAy_2025-08-17_132754.jpg
2025-08-17 13:31:34 | INFO     | src.invoice_processor:process_batch:259 - Batch processing completed: 1/1 successful
2025-08-17 13:31:34 | SUCCESS  | src.excel_manager:save_invoice_data:82 - Invoice data saved to: G:\vscodeapps\augment\invoicekeeper\data\output\Invoices_2025.xlsx
2025-08-18 13:36:43 | INFO     | src.logging_config:setup_logging:73 - Logging system initialized
2025-08-18 13:36:43 | INFO     | src.ai_manager:__init__:35 - AI Manager initialized with primary: openrouter
2025-08-18 13:36:43 | INFO     | src.ai_manager:__init__:36 - Available providers: ['deepseek', 'openrouter', 'ollama']
2025-08-18 13:36:43 | INFO     | src.invoice_processor:__init__:78 - Invoice Processor initialized
2025-08-18 13:36:43 | INFO     | src.excel_manager:__init__:40 - Excel Manager initialized
2025-08-18 13:36:44 | INFO     | src.ai_manager:__init__:35 - AI Manager initialized with primary: openrouter
2025-08-18 13:36:44 | INFO     | src.ai_manager:__init__:36 - Available providers: ['deepseek', 'openrouter', 'ollama']
2025-08-18 13:36:44 | INFO     | src.invoice_processor:discover_invoice_files:289 - Discovered 2 invoice files in G:\vscodeapps\augment\invoicekeeper\data\invoices
2025-08-18 13:36:45 | INFO     | src.invoice_processor:process_batch:232 - Starting batch processing of 2 files
2025-08-18 13:36:45 | INFO     | src.invoice_processor:process_single_invoice:100 - Processing invoice: G:\vscodeapps\augment\invoicekeeper\data\invoices\Amber_2025-08-18_133106.jpg
2025-08-18 13:36:45 | DEBUG    | src.utils:enhance_image_pil:104 - Enhanced image saved to: G:\vscodeapps\augment\invoicekeeper\data\processed\enhanced_Amber_2025-08-18_133106.jpg
2025-08-18 13:36:45 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: openrouter
2025-08-18 13:36:46 | INFO     | src.invoice_processor:process_single_invoice:100 - Processing invoice: G:\vscodeapps\augment\invoicekeeper\data\invoices\DayToDay_2025-08-18_133256.jpg
2025-08-18 13:36:46 | DEBUG    | src.utils:enhance_image_pil:104 - Enhanced image saved to: G:\vscodeapps\augment\invoicekeeper\data\processed\enhanced_DayToDay_2025-08-18_133256.jpg
2025-08-18 13:36:46 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: openrouter
2025-08-18 13:36:53 | SUCCESS  | src.ai_manager:process_invoice:76 - Successfully processed with openrouter
2025-08-18 13:36:53 | SUCCESS  | src.invoice_processor:process_single_invoice:177 - Successfully processed G:\vscodeapps\augment\invoicekeeper\data\invoices\Amber_2025-08-18_133106.jpg in 8.37s
2025-08-18 13:36:53 | DEBUG    | src.invoice_processor:move_file_to_processed:319 - Moved successful file to: G:\vscodeapps\augment\invoicekeeper\data\processed\2025\08\Amber_2025-08-18_133106.jpg
2025-08-18 13:36:53 | INFO     | src.invoice_processor:process_single_invoice:182 - Moved successful file to: G:\vscodeapps\augment\invoicekeeper\data\processed\2025\08\Amber_2025-08-18_133106.jpg
2025-08-18 13:36:54 | SUCCESS  | src.ai_manager:process_invoice:76 - Successfully processed with openrouter
2025-08-18 13:36:54 | SUCCESS  | src.invoice_processor:process_single_invoice:177 - Successfully processed G:\vscodeapps\augment\invoicekeeper\data\invoices\DayToDay_2025-08-18_133256.jpg in 8.29s
2025-08-18 13:36:54 | DEBUG    | src.invoice_processor:move_file_to_processed:319 - Moved successful file to: G:\vscodeapps\augment\invoicekeeper\data\processed\2025\08\DayToDay_2025-08-18_133256.jpg
2025-08-18 13:36:54 | INFO     | src.invoice_processor:process_single_invoice:182 - Moved successful file to: G:\vscodeapps\augment\invoicekeeper\data\processed\2025\08\DayToDay_2025-08-18_133256.jpg
2025-08-18 13:36:54 | INFO     | src.invoice_processor:process_batch:259 - Batch processing completed: 2/2 successful
2025-08-18 13:36:54 | SUCCESS  | src.excel_manager:save_invoice_data:82 - Invoice data saved to: G:\vscodeapps\augment\invoicekeeper\data\output\Invoices_2025.xlsx
2025-08-23 10:09:38 | INFO     | src.logging_config:setup_logging:73 - Logging system initialized
2025-08-23 10:09:38 | INFO     | src.ai_manager:__init__:35 - AI Manager initialized with primary: openrouter
2025-08-23 10:09:38 | INFO     | src.ai_manager:__init__:36 - Available providers: ['deepseek', 'openrouter', 'ollama']
2025-08-23 10:09:38 | INFO     | src.invoice_processor:__init__:78 - Invoice Processor initialized
2025-08-23 10:09:39 | INFO     | src.excel_manager:__init__:40 - Excel Manager initialized
2025-08-23 10:09:39 | INFO     | src.ai_manager:__init__:35 - AI Manager initialized with primary: openrouter
2025-08-23 10:09:39 | INFO     | src.ai_manager:__init__:36 - Available providers: ['deepseek', 'openrouter', 'ollama']
2025-08-23 10:09:39 | INFO     | src.invoice_processor:discover_invoice_files:289 - Discovered 1 invoice files in G:\vscodeapps\augment\invoicekeeper\data\invoices
2025-08-23 10:09:42 | INFO     | src.invoice_processor:process_batch:232 - Starting batch processing of 1 files
2025-08-23 10:09:42 | INFO     | src.invoice_processor:process_single_invoice:100 - Processing invoice: G:\vscodeapps\augment\invoicekeeper\data\invoices\Noor_2025-08-23_100424.jpg
2025-08-23 10:09:42 | DEBUG    | src.utils:enhance_image_pil:104 - Enhanced image saved to: G:\vscodeapps\augment\invoicekeeper\data\processed\enhanced_Noor_2025-08-23_100424.jpg
2025-08-23 10:09:42 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: openrouter
2025-08-23 10:09:50 | SUCCESS  | src.ai_manager:process_invoice:76 - Successfully processed with openrouter
2025-08-23 10:09:50 | SUCCESS  | src.invoice_processor:process_single_invoice:177 - Successfully processed G:\vscodeapps\augment\invoicekeeper\data\invoices\Noor_2025-08-23_100424.jpg in 8.49s
2025-08-23 10:09:50 | DEBUG    | src.invoice_processor:move_file_to_processed:319 - Moved successful file to: G:\vscodeapps\augment\invoicekeeper\data\processed\2025\08\Noor_2025-08-23_100424.jpg
2025-08-23 10:09:50 | INFO     | src.invoice_processor:process_single_invoice:182 - Moved successful file to: G:\vscodeapps\augment\invoicekeeper\data\processed\2025\08\Noor_2025-08-23_100424.jpg
2025-08-23 10:09:50 | INFO     | src.invoice_processor:process_batch:259 - Batch processing completed: 1/1 successful
2025-08-23 10:09:50 | SUCCESS  | src.excel_manager:save_invoice_data:82 - Invoice data saved to: G:\vscodeapps\augment\invoicekeeper\data\output\Invoices_2025.xlsx
2025-08-23 10:14:09 | INFO     | src.logging_config:setup_logging:73 - Logging system initialized
2025-08-23 10:14:09 | INFO     | src.ai_manager:__init__:35 - AI Manager initialized with primary: openrouter
2025-08-23 10:14:09 | INFO     | src.ai_manager:__init__:36 - Available providers: ['deepseek', 'openrouter', 'ollama']
2025-08-23 10:14:09 | INFO     | src.invoice_processor:__init__:78 - Invoice Processor initialized
2025-08-23 10:14:09 | INFO     | src.excel_manager:__init__:40 - Excel Manager initialized
2025-08-23 10:14:10 | INFO     | src.ai_manager:__init__:35 - AI Manager initialized with primary: openrouter
2025-08-23 10:14:10 | INFO     | src.ai_manager:__init__:36 - Available providers: ['deepseek', 'openrouter', 'ollama']
2025-08-23 10:16:45 | INFO     | src.logging_config:setup_logging:73 - Logging system initialized
2025-08-26 16:33:09 | INFO     | src.logging_config:setup_logging:73 - Logging system initialized
2025-08-26 16:33:10 | INFO     | src.ai_manager:__init__:35 - AI Manager initialized with primary: openrouter
2025-08-26 16:33:10 | INFO     | src.ai_manager:__init__:36 - Available providers: ['deepseek', 'openrouter', 'ollama']
2025-08-26 16:33:10 | INFO     | src.invoice_processor:__init__:78 - Invoice Processor initialized
2025-08-26 16:33:10 | INFO     | src.excel_manager:__init__:40 - Excel Manager initialized
2025-08-26 16:33:11 | INFO     | src.ai_manager:__init__:35 - AI Manager initialized with primary: openrouter
2025-08-26 16:33:11 | INFO     | src.ai_manager:__init__:36 - Available providers: ['deepseek', 'openrouter', 'ollama']
2025-08-26 16:33:11 | INFO     | src.invoice_processor:discover_invoice_files:289 - Discovered 2 invoice files in G:\vscodeapps\augment\invoicekeeper\data\invoices
2025-08-26 16:33:13 | INFO     | src.invoice_processor:process_batch:232 - Starting batch processing of 2 files
2025-08-26 16:33:13 | INFO     | src.invoice_processor:process_single_invoice:100 - Processing invoice: G:\vscodeapps\augment\invoicekeeper\data\invoices\Amber 122.jpg
2025-08-26 16:33:13 | DEBUG    | src.utils:enhance_image_pil:104 - Enhanced image saved to: G:\vscodeapps\augment\invoicekeeper\data\processed\enhanced_Amber 122.jpg
2025-08-26 16:33:13 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: openrouter
2025-08-26 16:33:13 | INFO     | src.invoice_processor:process_single_invoice:100 - Processing invoice: G:\vscodeapps\augment\invoicekeeper\data\invoices\Amber 8.jpg
2025-08-26 16:33:14 | DEBUG    | src.utils:enhance_image_pil:104 - Enhanced image saved to: G:\vscodeapps\augment\invoicekeeper\data\processed\enhanced_Amber 8.jpg
2025-08-26 16:33:14 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: openrouter
2025-08-26 16:33:22 | SUCCESS  | src.ai_manager:process_invoice:76 - Successfully processed with openrouter
2025-08-26 16:33:22 | SUCCESS  | src.invoice_processor:process_single_invoice:177 - Successfully processed G:\vscodeapps\augment\invoicekeeper\data\invoices\Amber 8.jpg in 8.30s
2025-08-26 16:33:22 | DEBUG    | src.invoice_processor:move_file_to_processed:319 - Moved successful file to: G:\vscodeapps\augment\invoicekeeper\data\processed\2025\08\Amber 8.jpg
2025-08-26 16:33:22 | INFO     | src.invoice_processor:process_single_invoice:182 - Moved successful file to: G:\vscodeapps\augment\invoicekeeper\data\processed\2025\08\Amber 8.jpg
2025-08-26 16:33:22 | SUCCESS  | src.ai_manager:process_invoice:76 - Successfully processed with openrouter
2025-08-26 16:33:22 | SUCCESS  | src.invoice_processor:process_single_invoice:177 - Successfully processed G:\vscodeapps\augment\invoicekeeper\data\invoices\Amber 122.jpg in 9.10s
2025-08-26 16:33:22 | DEBUG    | src.invoice_processor:move_file_to_processed:319 - Moved successful file to: G:\vscodeapps\augment\invoicekeeper\data\processed\2025\08\Amber 122.jpg
2025-08-26 16:33:22 | INFO     | src.invoice_processor:process_single_invoice:182 - Moved successful file to: G:\vscodeapps\augment\invoicekeeper\data\processed\2025\08\Amber 122.jpg
2025-08-26 16:33:22 | INFO     | src.invoice_processor:process_batch:259 - Batch processing completed: 2/2 successful
2025-08-26 16:33:22 | SUCCESS  | src.excel_manager:save_invoice_data:82 - Invoice data saved to: G:\vscodeapps\augment\invoicekeeper\data\output\Invoices_2025.xlsx
2025-08-31 14:44:32 | INFO     | src.logging_config:setup_logging:73 - Logging system initialized
2025-08-31 14:44:37 | INFO     | src.ai_manager:__init__:35 - AI Manager initialized with primary: openrouter
2025-08-31 14:44:37 | INFO     | src.ai_manager:__init__:36 - Available providers: ['deepseek', 'openrouter']
2025-08-31 14:44:37 | INFO     | src.invoice_processor:__init__:78 - Invoice Processor initialized
2025-08-31 14:44:37 | INFO     | src.excel_manager:__init__:40 - Excel Manager initialized
2025-08-31 14:44:41 | INFO     | src.ai_manager:__init__:35 - AI Manager initialized with primary: openrouter
2025-08-31 14:44:41 | INFO     | src.ai_manager:__init__:36 - Available providers: ['deepseek', 'openrouter']
2025-08-31 14:44:41 | INFO     | src.invoice_processor:discover_invoice_files:289 - Discovered 25 invoice files in G:\vscodeapps\augment\invoicekeeper\data\invoices
2025-08-31 14:44:47 | INFO     | src.invoice_processor:process_batch:232 - Starting batch processing of 5 files
2025-08-31 14:44:47 | INFO     | src.invoice_processor:process_single_invoice:100 - Processing invoice: G:\vscodeapps\augment\invoicekeeper\data\invoices\Amber 122.jpg
2025-08-31 14:44:47 | DEBUG    | src.utils:enhance_image_pil:104 - Enhanced image saved to: G:\vscodeapps\augment\invoicekeeper\data\processed\enhanced_Amber 122.jpg
2025-08-31 14:44:47 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: deepseek
2025-08-31 14:44:50 | INFO     | src.invoice_processor:process_single_invoice:100 - Processing invoice: G:\vscodeapps\augment\invoicekeeper\data\invoices\Amber 8.jpg
2025-08-31 14:44:50 | DEBUG    | src.utils:enhance_image_pil:104 - Enhanced image saved to: G:\vscodeapps\augment\invoicekeeper\data\processed\enhanced_Amber 8.jpg
2025-08-31 14:44:50 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: deepseek
2025-08-31 14:44:52 | INFO     | src.invoice_processor:process_single_invoice:100 - Processing invoice: G:\vscodeapps\augment\invoicekeeper\data\invoices\Amber_2025-08-18_133106.jpg
2025-08-31 14:44:52 | DEBUG    | src.utils:enhance_image_pil:104 - Enhanced image saved to: G:\vscodeapps\augment\invoicekeeper\data\processed\enhanced_Amber_2025-08-18_133106.jpg
2025-08-31 14:44:52 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: deepseek
2025-08-31 14:44:54 | INFO     | src.invoice_processor:process_single_invoice:100 - Processing invoice: G:\vscodeapps\augment\invoicekeeper\data\invoices\Amber_2025-08-29_13472_122.jpg
2025-08-31 14:44:54 | DEBUG    | src.utils:enhance_image_pil:104 - Enhanced image saved to: G:\vscodeapps\augment\invoicekeeper\data\processed\enhanced_Amber_2025-08-29_13472_122.jpg
2025-08-31 14:44:54 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: deepseek
2025-08-31 14:44:55 | INFO     | src.invoice_processor:process_single_invoice:100 - Processing invoice: G:\vscodeapps\augment\invoicekeeper\data\invoices\Amber_2025-08-31_134311_244.jpg
2025-08-31 14:44:56 | DEBUG    | src.utils:enhance_image_pil:104 - Enhanced image saved to: G:\vscodeapps\augment\invoicekeeper\data\processed\enhanced_Amber_2025-08-31_134311_244.jpg
2025-08-31 14:44:56 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: deepseek
2025-08-31 14:45:07 | SUCCESS  | src.ai_manager:process_invoice:76 - Successfully processed with deepseek
2025-08-31 14:45:07 | SUCCESS  | src.invoice_processor:process_single_invoice:177 - Successfully processed G:\vscodeapps\augment\invoicekeeper\data\invoices\Amber_2025-08-29_13472_122.jpg in 13.29s
2025-08-31 14:45:07 | DEBUG    | src.invoice_processor:move_file_to_processed:319 - Moved successful file to: G:\vscodeapps\augment\invoicekeeper\data\processed\2025\08\Amber_2025-08-29_13472_122.jpg
2025-08-31 14:45:07 | INFO     | src.invoice_processor:process_single_invoice:182 - Moved successful file to: G:\vscodeapps\augment\invoicekeeper\data\processed\2025\08\Amber_2025-08-29_13472_122.jpg
2025-08-31 14:45:07 | SUCCESS  | src.ai_manager:process_invoice:76 - Successfully processed with deepseek
2025-08-31 14:45:07 | SUCCESS  | src.invoice_processor:process_single_invoice:177 - Successfully processed G:\vscodeapps\augment\invoicekeeper\data\invoices\Amber 122.jpg in 20.61s
2025-08-31 14:45:07 | DEBUG    | src.invoice_processor:move_file_to_processed:319 - Moved successful file to: G:\vscodeapps\augment\invoicekeeper\data\processed\2025\08\Amber 122.jpg
2025-08-31 14:45:07 | INFO     | src.invoice_processor:process_single_invoice:182 - Moved successful file to: G:\vscodeapps\augment\invoicekeeper\data\processed\2025\08\Amber 122.jpg
2025-08-31 14:45:09 | SUCCESS  | src.ai_manager:process_invoice:76 - Successfully processed with deepseek
2025-08-31 14:45:09 | SUCCESS  | src.invoice_processor:process_single_invoice:177 - Successfully processed G:\vscodeapps\augment\invoicekeeper\data\invoices\Amber_2025-08-31_134311_244.jpg in 13.94s
2025-08-31 14:45:09 | DEBUG    | src.invoice_processor:move_file_to_processed:319 - Moved successful file to: G:\vscodeapps\augment\invoicekeeper\data\processed\2025\08\Amber_2025-08-31_134311_244.jpg
2025-08-31 14:45:09 | INFO     | src.invoice_processor:process_single_invoice:182 - Moved successful file to: G:\vscodeapps\augment\invoicekeeper\data\processed\2025\08\Amber_2025-08-31_134311_244.jpg
2025-08-31 14:45:10 | SUCCESS  | src.ai_manager:process_invoice:76 - Successfully processed with deepseek
2025-08-31 14:45:10 | SUCCESS  | src.invoice_processor:process_single_invoice:177 - Successfully processed G:\vscodeapps\augment\invoicekeeper\data\invoices\Amber 8.jpg in 19.48s
2025-08-31 14:45:10 | DEBUG    | src.invoice_processor:move_file_to_processed:319 - Moved successful file to: G:\vscodeapps\augment\invoicekeeper\data\processed\2025\08\Amber 8.jpg
2025-08-31 14:45:10 | INFO     | src.invoice_processor:process_single_invoice:182 - Moved successful file to: G:\vscodeapps\augment\invoicekeeper\data\processed\2025\08\Amber 8.jpg
2025-08-31 14:45:11 | SUCCESS  | src.ai_manager:process_invoice:76 - Successfully processed with deepseek
2025-08-31 14:45:11 | SUCCESS  | src.invoice_processor:process_single_invoice:177 - Successfully processed G:\vscodeapps\augment\invoicekeeper\data\invoices\Amber_2025-08-18_133106.jpg in 19.33s
2025-08-31 14:45:11 | DEBUG    | src.invoice_processor:move_file_to_processed:319 - Moved successful file to: G:\vscodeapps\augment\invoicekeeper\data\processed\2025\08\Amber_2025-08-18_133106.jpg
2025-08-31 14:45:11 | INFO     | src.invoice_processor:process_single_invoice:182 - Moved successful file to: G:\vscodeapps\augment\invoicekeeper\data\processed\2025\08\Amber_2025-08-18_133106.jpg
2025-08-31 14:45:11 | INFO     | src.invoice_processor:process_batch:259 - Batch processing completed: 5/5 successful
2025-08-31 14:45:11 | INFO     | src.invoice_processor:process_batch:232 - Starting batch processing of 5 files
2025-08-31 14:45:11 | INFO     | src.invoice_processor:process_single_invoice:100 - Processing invoice: G:\vscodeapps\augment\invoicekeeper\data\invoices\Amber_2025-08-31_134311_534.jpg
2025-08-31 14:45:11 | DEBUG    | src.utils:enhance_image_pil:104 - Enhanced image saved to: G:\vscodeapps\augment\invoicekeeper\data\processed\enhanced_Amber_2025-08-31_134311_534.jpg
2025-08-31 14:45:11 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: deepseek
2025-08-31 14:45:13 | INFO     | src.invoice_processor:process_single_invoice:100 - Processing invoice: G:\vscodeapps\augment\invoicekeeper\data\invoices\Amber_256.jpg
2025-08-31 14:45:13 | DEBUG    | src.utils:enhance_image_pil:104 - Enhanced image saved to: G:\vscodeapps\augment\invoicekeeper\data\processed\enhanced_Amber_256.jpg
2025-08-31 14:45:13 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: deepseek
2025-08-31 14:45:14 | INFO     | src.invoice_processor:process_single_invoice:100 - Processing invoice: G:\vscodeapps\augment\invoicekeeper\data\invoices\Amber_43.jpg
2025-08-31 14:45:15 | DEBUG    | src.utils:enhance_image_pil:104 - Enhanced image saved to: G:\vscodeapps\augment\invoicekeeper\data\processed\enhanced_Amber_43.jpg
2025-08-31 14:45:15 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: deepseek
2025-08-31 14:45:16 | INFO     | src.invoice_processor:process_single_invoice:100 - Processing invoice: G:\vscodeapps\augment\invoicekeeper\data\invoices\Calicut_20250810160800.jpg
2025-08-31 14:45:16 | DEBUG    | src.utils:enhance_image_pil:104 - Enhanced image saved to: G:\vscodeapps\augment\invoicekeeper\data\processed\enhanced_Calicut_20250810160800.jpg
2025-08-31 14:45:16 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: deepseek
2025-08-31 14:45:18 | INFO     | src.invoice_processor:process_single_invoice:100 - Processing invoice: G:\vscodeapps\augment\invoicekeeper\data\invoices\DayToDAy_15.jpg
2025-08-31 14:45:18 | DEBUG    | src.utils:enhance_image_pil:104 - Enhanced image saved to: G:\vscodeapps\augment\invoicekeeper\data\processed\enhanced_DayToDAy_15.jpg
2025-08-31 14:45:18 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: deepseek
2025-08-31 14:45:29 | WARNING  | src.ai_manager:process_invoice:80 - Low confidence result from deepseek
2025-08-31 14:45:29 | ERROR    | src.invoice_processor:process_single_invoice:201 - Failed to process G:\vscodeapps\augment\invoicekeeper\data\invoices\Amber_256.jpg: All providers failed. Last error: None
2025-08-31 14:45:29 | DEBUG    | src.invoice_processor:move_file_to_failed:354 - Moved failed file to: G:\vscodeapps\augment\invoicekeeper\data\failed\2025\08\Amber_256.jpg
2025-08-31 14:45:29 | INFO     | src.invoice_processor:process_single_invoice:206 - Moved failed file to: G:\vscodeapps\augment\invoicekeeper\data\failed\2025\08\Amber_256.jpg
2025-08-31 14:45:32 | SUCCESS  | src.ai_manager:process_invoice:76 - Successfully processed with deepseek
2025-08-31 14:45:32 | SUCCESS  | src.invoice_processor:process_single_invoice:177 - Successfully processed G:\vscodeapps\augment\invoicekeeper\data\invoices\Amber_2025-08-31_134311_534.jpg in 20.69s
2025-08-31 14:45:32 | DEBUG    | src.invoice_processor:move_file_to_processed:319 - Moved successful file to: G:\vscodeapps\augment\invoicekeeper\data\processed\2025\08\Amber_2025-08-31_134311_534.jpg
2025-08-31 14:45:32 | INFO     | src.invoice_processor:process_single_invoice:182 - Moved successful file to: G:\vscodeapps\augment\invoicekeeper\data\processed\2025\08\Amber_2025-08-31_134311_534.jpg
2025-08-31 14:45:38 | SUCCESS  | src.ai_manager:process_invoice:76 - Successfully processed with deepseek
2025-08-31 14:45:38 | SUCCESS  | src.invoice_processor:process_single_invoice:177 - Successfully processed G:\vscodeapps\augment\invoicekeeper\data\invoices\DayToDAy_15.jpg in 20.32s
2025-08-31 14:45:38 | DEBUG    | src.invoice_processor:move_file_to_processed:319 - Moved successful file to: G:\vscodeapps\augment\invoicekeeper\data\processed\2025\08\DayToDAy_15.jpg
2025-08-31 14:45:38 | INFO     | src.invoice_processor:process_single_invoice:182 - Moved successful file to: G:\vscodeapps\augment\invoicekeeper\data\processed\2025\08\DayToDAy_15.jpg
2025-08-31 14:45:39 | SUCCESS  | src.ai_manager:process_invoice:76 - Successfully processed with deepseek
2025-08-31 14:45:39 | SUCCESS  | src.invoice_processor:process_single_invoice:177 - Successfully processed G:\vscodeapps\augment\invoicekeeper\data\invoices\Amber_43.jpg in 25.08s
2025-08-31 14:45:39 | DEBUG    | src.invoice_processor:move_file_to_processed:319 - Moved successful file to: G:\vscodeapps\augment\invoicekeeper\data\processed\2025\08\Amber_43.jpg
2025-08-31 14:45:39 | INFO     | src.invoice_processor:process_single_invoice:182 - Moved successful file to: G:\vscodeapps\augment\invoicekeeper\data\processed\2025\08\Amber_43.jpg
2025-08-31 14:45:43 | SUCCESS  | src.ai_manager:process_invoice:76 - Successfully processed with deepseek
2025-08-31 14:45:43 | SUCCESS  | src.invoice_processor:process_single_invoice:177 - Successfully processed G:\vscodeapps\augment\invoicekeeper\data\invoices\Calicut_20250810160800.jpg in 26.83s
2025-08-31 14:45:43 | DEBUG    | src.invoice_processor:move_file_to_processed:319 - Moved successful file to: G:\vscodeapps\augment\invoicekeeper\data\processed\2025\08\Calicut_20250810160800.jpg
2025-08-31 14:45:43 | INFO     | src.invoice_processor:process_single_invoice:182 - Moved successful file to: G:\vscodeapps\augment\invoicekeeper\data\processed\2025\08\Calicut_20250810160800.jpg
2025-08-31 14:45:43 | INFO     | src.invoice_processor:process_batch:259 - Batch processing completed: 4/5 successful
2025-08-31 14:45:43 | INFO     | src.invoice_processor:process_batch:232 - Starting batch processing of 5 files
2025-08-31 14:45:43 | INFO     | src.invoice_processor:process_single_invoice:100 - Processing invoice: G:\vscodeapps\augment\invoicekeeper\data\invoices\DayToDAy_17.jpg
2025-08-31 14:45:43 | DEBUG    | src.utils:enhance_image_pil:104 - Enhanced image saved to: G:\vscodeapps\augment\invoicekeeper\data\processed\enhanced_DayToDAy_17.jpg
2025-08-31 14:45:43 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: deepseek
2025-08-31 14:45:45 | INFO     | src.invoice_processor:process_single_invoice:100 - Processing invoice: G:\vscodeapps\augment\invoicekeeper\data\invoices\DayToDAy_2025-08-17_132754.jpg
2025-08-31 14:45:45 | DEBUG    | src.utils:enhance_image_pil:104 - Enhanced image saved to: G:\vscodeapps\augment\invoicekeeper\data\processed\enhanced_DayToDAy_2025-08-17_132754.jpg
2025-08-31 14:45:45 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: deepseek
2025-08-31 14:45:46 | INFO     | src.invoice_processor:process_single_invoice:100 - Processing invoice: G:\vscodeapps\augment\invoicekeeper\data\invoices\DayToDay_2025-08-18_133256.jpg
2025-08-31 14:45:47 | DEBUG    | src.utils:enhance_image_pil:104 - Enhanced image saved to: G:\vscodeapps\augment\invoicekeeper\data\processed\enhanced_DayToDay_2025-08-18_133256.jpg
2025-08-31 14:45:47 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: deepseek
2025-08-31 14:45:48 | INFO     | src.invoice_processor:process_single_invoice:100 - Processing invoice: G:\vscodeapps\augment\invoicekeeper\data\invoices\DayToDAy_83.jpg
2025-08-31 14:45:48 | DEBUG    | src.utils:enhance_image_pil:104 - Enhanced image saved to: G:\vscodeapps\augment\invoicekeeper\data\processed\enhanced_DayToDAy_83.jpg
2025-08-31 14:45:48 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: deepseek
2025-08-31 14:45:50 | INFO     | src.invoice_processor:process_single_invoice:100 - Processing invoice: G:\vscodeapps\augment\invoicekeeper\data\invoices\Fancy_2025-08-29_134729.jpg
2025-08-31 14:45:50 | DEBUG    | src.utils:enhance_image_pil:104 - Enhanced image saved to: G:\vscodeapps\augment\invoicekeeper\data\processed\enhanced_Fancy_2025-08-29_134729.jpg
2025-08-31 14:45:50 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: deepseek
2025-08-31 14:46:02 | SUCCESS  | src.ai_manager:process_invoice:76 - Successfully processed with deepseek
2025-08-31 14:46:02 | SUCCESS  | src.invoice_processor:process_single_invoice:177 - Successfully processed G:\vscodeapps\augment\invoicekeeper\data\invoices\DayToDAy_17.jpg in 19.12s
2025-08-31 14:46:02 | DEBUG    | src.invoice_processor:move_file_to_processed:319 - Moved successful file to: G:\vscodeapps\augment\invoicekeeper\data\processed\2025\08\DayToDAy_17.jpg
2025-08-31 14:46:02 | INFO     | src.invoice_processor:process_single_invoice:182 - Moved successful file to: G:\vscodeapps\augment\invoicekeeper\data\processed\2025\08\DayToDAy_17.jpg
2025-08-31 14:46:04 | SUCCESS  | src.ai_manager:process_invoice:76 - Successfully processed with deepseek
2025-08-31 14:46:04 | SUCCESS  | src.invoice_processor:process_single_invoice:177 - Successfully processed G:\vscodeapps\augment\invoicekeeper\data\invoices\DayToDay_2025-08-18_133256.jpg in 17.60s
2025-08-31 14:46:04 | DEBUG    | src.invoice_processor:move_file_to_processed:319 - Moved successful file to: G:\vscodeapps\augment\invoicekeeper\data\processed\2025\08\DayToDay_2025-08-18_133256.jpg
2025-08-31 14:46:04 | INFO     | src.invoice_processor:process_single_invoice:182 - Moved successful file to: G:\vscodeapps\augment\invoicekeeper\data\processed\2025\08\DayToDay_2025-08-18_133256.jpg
2025-08-31 14:46:04 | SUCCESS  | src.ai_manager:process_invoice:76 - Successfully processed with deepseek
2025-08-31 14:46:04 | SUCCESS  | src.invoice_processor:process_single_invoice:177 - Successfully processed G:\vscodeapps\augment\invoicekeeper\data\invoices\DayToDAy_2025-08-17_132754.jpg in 19.74s
2025-08-31 14:46:04 | DEBUG    | src.invoice_processor:move_file_to_processed:319 - Moved successful file to: G:\vscodeapps\augment\invoicekeeper\data\processed\2025\08\DayToDAy_2025-08-17_132754.jpg
2025-08-31 14:46:04 | INFO     | src.invoice_processor:process_single_invoice:182 - Moved successful file to: G:\vscodeapps\augment\invoicekeeper\data\processed\2025\08\DayToDAy_2025-08-17_132754.jpg
2025-08-31 14:46:06 | SUCCESS  | src.ai_manager:process_invoice:76 - Successfully processed with deepseek
2025-08-31 14:46:06 | SUCCESS  | src.invoice_processor:process_single_invoice:177 - Successfully processed G:\vscodeapps\augment\invoicekeeper\data\invoices\Fancy_2025-08-29_134729.jpg in 15.93s
2025-08-31 14:46:06 | DEBUG    | src.invoice_processor:move_file_to_processed:319 - Moved successful file to: G:\vscodeapps\augment\invoicekeeper\data\processed\2025\08\Fancy_2025-08-29_134729.jpg
2025-08-31 14:46:06 | INFO     | src.invoice_processor:process_single_invoice:182 - Moved successful file to: G:\vscodeapps\augment\invoicekeeper\data\processed\2025\08\Fancy_2025-08-29_134729.jpg
2025-08-31 14:46:08 | SUCCESS  | src.ai_manager:process_invoice:76 - Successfully processed with deepseek
2025-08-31 14:46:08 | SUCCESS  | src.invoice_processor:process_single_invoice:177 - Successfully processed G:\vscodeapps\augment\invoicekeeper\data\invoices\DayToDAy_83.jpg in 20.31s
2025-08-31 14:46:08 | DEBUG    | src.invoice_processor:move_file_to_processed:319 - Moved successful file to: G:\vscodeapps\augment\invoicekeeper\data\processed\2025\08\DayToDAy_83.jpg
2025-08-31 14:46:08 | INFO     | src.invoice_processor:process_single_invoice:182 - Moved successful file to: G:\vscodeapps\augment\invoicekeeper\data\processed\2025\08\DayToDAy_83.jpg
2025-08-31 14:46:08 | INFO     | src.invoice_processor:process_batch:259 - Batch processing completed: 5/5 successful
2025-08-31 14:46:08 | INFO     | src.invoice_processor:process_batch:232 - Starting batch processing of 5 files
2025-08-31 14:46:08 | INFO     | src.invoice_processor:process_single_invoice:100 - Processing invoice: G:\vscodeapps\augment\invoicekeeper\data\invoices\Invoice-1.png
2025-08-31 14:46:09 | DEBUG    | src.utils:enhance_image_pil:104 - Enhanced image saved to: G:\vscodeapps\augment\invoicekeeper\data\processed\enhanced_Invoice-1.png
2025-08-31 14:46:09 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: deepseek
2025-08-31 14:46:10 | INFO     | src.invoice_processor:process_single_invoice:100 - Processing invoice: G:\vscodeapps\augment\invoicekeeper\data\invoices\Invoice-2.jpg
2025-08-31 14:46:10 | DEBUG    | src.utils:enhance_image_pil:104 - Enhanced image saved to: G:\vscodeapps\augment\invoicekeeper\data\processed\enhanced_Invoice-2.jpg
2025-08-31 14:46:10 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: deepseek
2025-08-31 14:46:13 | INFO     | src.invoice_processor:process_single_invoice:100 - Processing invoice: G:\vscodeapps\augment\invoicekeeper\data\invoices\Invoice-4.jpg
2025-08-31 14:46:14 | DEBUG    | src.utils:enhance_image_pil:104 - Enhanced image saved to: G:\vscodeapps\augment\invoicekeeper\data\processed\enhanced_Invoice-4.jpg
2025-08-31 14:46:14 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: deepseek
2025-08-31 14:46:15 | INFO     | src.invoice_processor:process_single_invoice:100 - Processing invoice: G:\vscodeapps\augment\invoicekeeper\data\invoices\Invoice_2025-08-14_130400.jpg
2025-08-31 14:46:15 | DEBUG    | src.utils:enhance_image_pil:104 - Enhanced image saved to: G:\vscodeapps\augment\invoicekeeper\data\processed\enhanced_Invoice_2025-08-14_130400.jpg
2025-08-31 14:46:15 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: deepseek
2025-08-31 14:46:17 | INFO     | src.invoice_processor:process_single_invoice:100 - Processing invoice: G:\vscodeapps\augment\invoicekeeper\data\invoices\NESTO_2025-08-31_141544.jpg
2025-08-31 14:46:17 | DEBUG    | src.utils:enhance_image_pil:104 - Enhanced image saved to: G:\vscodeapps\augment\invoicekeeper\data\processed\enhanced_NESTO_2025-08-31_141544.jpg
2025-08-31 14:46:17 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: deepseek
2025-08-31 14:46:28 | SUCCESS  | src.ai_manager:process_invoice:76 - Successfully processed with deepseek
2025-08-31 14:46:28 | SUCCESS  | src.invoice_processor:process_single_invoice:177 - Successfully processed G:\vscodeapps\augment\invoicekeeper\data\invoices\Invoice-1.png in 19.51s
2025-08-31 14:46:28 | DEBUG    | src.invoice_processor:move_file_to_processed:319 - Moved successful file to: G:\vscodeapps\augment\invoicekeeper\data\processed\2025\08\Invoice-1.png
2025-08-31 14:46:28 | INFO     | src.invoice_processor:process_single_invoice:182 - Moved successful file to: G:\vscodeapps\augment\invoicekeeper\data\processed\2025\08\Invoice-1.png
2025-08-31 14:46:29 | SUCCESS  | src.ai_manager:process_invoice:76 - Successfully processed with deepseek
2025-08-31 14:46:29 | SUCCESS  | src.invoice_processor:process_single_invoice:177 - Successfully processed G:\vscodeapps\augment\invoicekeeper\data\invoices\NESTO_2025-08-31_141544.jpg in 11.67s
2025-08-31 14:46:29 | DEBUG    | src.invoice_processor:move_file_to_processed:319 - Moved successful file to: G:\vscodeapps\augment\invoicekeeper\data\processed\2025\08\NESTO_2025-08-31_141544.jpg
2025-08-31 14:46:29 | INFO     | src.invoice_processor:process_single_invoice:182 - Moved successful file to: G:\vscodeapps\augment\invoicekeeper\data\processed\2025\08\NESTO_2025-08-31_141544.jpg
2025-08-31 14:46:32 | SUCCESS  | src.ai_manager:process_invoice:76 - Successfully processed with deepseek
2025-08-31 14:46:32 | SUCCESS  | src.invoice_processor:process_single_invoice:177 - Successfully processed G:\vscodeapps\augment\invoicekeeper\data\invoices\Invoice-4.jpg in 18.88s
2025-08-31 14:46:32 | DEBUG    | src.invoice_processor:move_file_to_processed:319 - Moved successful file to: G:\vscodeapps\augment\invoicekeeper\data\processed\2025\08\Invoice-4.jpg
2025-08-31 14:46:32 | INFO     | src.invoice_processor:process_single_invoice:182 - Moved successful file to: G:\vscodeapps\augment\invoicekeeper\data\processed\2025\08\Invoice-4.jpg
2025-08-31 14:46:39 | SUCCESS  | src.ai_manager:process_invoice:76 - Successfully processed with deepseek
2025-08-31 14:46:39 | SUCCESS  | src.invoice_processor:process_single_invoice:177 - Successfully processed G:\vscodeapps\augment\invoicekeeper\data\invoices\Invoice_2025-08-14_130400.jpg in 24.05s
2025-08-31 14:46:39 | DEBUG    | src.invoice_processor:move_file_to_processed:319 - Moved successful file to: G:\vscodeapps\augment\invoicekeeper\data\processed\2025\08\Invoice_2025-08-14_130400.jpg
2025-08-31 14:46:39 | INFO     | src.invoice_processor:process_single_invoice:182 - Moved successful file to: G:\vscodeapps\augment\invoicekeeper\data\processed\2025\08\Invoice_2025-08-14_130400.jpg
2025-08-31 14:46:47 | SUCCESS  | src.ai_manager:process_invoice:76 - Successfully processed with deepseek
2025-08-31 14:46:47 | SUCCESS  | src.invoice_processor:process_single_invoice:177 - Successfully processed G:\vscodeapps\augment\invoicekeeper\data\invoices\Invoice-2.jpg in 37.20s
2025-08-31 14:46:47 | DEBUG    | src.invoice_processor:move_file_to_processed:319 - Moved successful file to: G:\vscodeapps\augment\invoicekeeper\data\processed\2025\08\Invoice-2.jpg
2025-08-31 14:46:47 | INFO     | src.invoice_processor:process_single_invoice:182 - Moved successful file to: G:\vscodeapps\augment\invoicekeeper\data\processed\2025\08\Invoice-2.jpg
2025-08-31 14:46:47 | INFO     | src.invoice_processor:process_batch:259 - Batch processing completed: 5/5 successful
2025-08-31 14:46:47 | INFO     | src.invoice_processor:process_batch:232 - Starting batch processing of 5 files
2025-08-31 14:46:47 | INFO     | src.invoice_processor:process_single_invoice:100 - Processing invoice: G:\vscodeapps\augment\invoicekeeper\data\invoices\NESTO_2025-08-31_141735.jpg
2025-08-31 14:46:47 | DEBUG    | src.utils:enhance_image_pil:104 - Enhanced image saved to: G:\vscodeapps\augment\invoicekeeper\data\processed\enhanced_NESTO_2025-08-31_141735.jpg
2025-08-31 14:46:47 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: deepseek
2025-08-31 14:46:49 | INFO     | src.invoice_processor:process_single_invoice:100 - Processing invoice: G:\vscodeapps\augment\invoicekeeper\data\invoices\Noor_2025-08-23_100424.jpg
2025-08-31 14:46:49 | DEBUG    | src.utils:enhance_image_pil:104 - Enhanced image saved to: G:\vscodeapps\augment\invoicekeeper\data\processed\enhanced_Noor_2025-08-23_100424.jpg
2025-08-31 14:46:49 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: deepseek
2025-08-31 14:46:50 | INFO     | src.invoice_processor:process_single_invoice:100 - Processing invoice: G:\vscodeapps\augment\invoicekeeper\data\invoices\Screenshot 2025-07-15 120050.png
2025-08-31 14:46:50 | DEBUG    | src.utils:enhance_image_pil:104 - Enhanced image saved to: G:\vscodeapps\augment\invoicekeeper\data\processed\enhanced_Screenshot 2025-07-15 120050.png
2025-08-31 14:46:50 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: deepseek
2025-08-31 14:46:51 | INFO     | src.invoice_processor:process_single_invoice:100 - Processing invoice: G:\vscodeapps\augment\invoicekeeper\data\invoices\Temu.png
2025-08-31 14:46:52 | DEBUG    | src.utils:enhance_image_pil:104 - Enhanced image saved to: G:\vscodeapps\augment\invoicekeeper\data\processed\enhanced_Temu.png
2025-08-31 14:46:52 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: deepseek
2025-08-31 14:46:53 | INFO     | src.invoice_processor:process_single_invoice:100 - Processing invoice: G:\vscodeapps\augment\invoicekeeper\data\invoices\VIVA_20250810160802_1.jpg
2025-08-31 14:46:53 | DEBUG    | src.utils:enhance_image_pil:104 - Enhanced image saved to: G:\vscodeapps\augment\invoicekeeper\data\processed\enhanced_VIVA_20250810160802_1.jpg
2025-08-31 14:46:53 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: deepseek
2025-08-31 14:47:05 | SUCCESS  | src.ai_manager:process_invoice:76 - Successfully processed with deepseek
2025-08-31 14:47:05 | SUCCESS  | src.invoice_processor:process_single_invoice:177 - Successfully processed G:\vscodeapps\augment\invoicekeeper\data\invoices\Screenshot 2025-07-15 120050.png in 14.34s
2025-08-31 14:47:05 | DEBUG    | src.invoice_processor:move_file_to_processed:319 - Moved successful file to: G:\vscodeapps\augment\invoicekeeper\data\processed\2025\08\Screenshot 2025-07-15 120050.png
2025-08-31 14:47:05 | INFO     | src.invoice_processor:process_single_invoice:182 - Moved successful file to: G:\vscodeapps\augment\invoicekeeper\data\processed\2025\08\Screenshot 2025-07-15 120050.png
2025-08-31 14:47:08 | WARNING  | src.ai_manager:process_invoice:80 - Low confidence result from deepseek
2025-08-31 14:47:08 | ERROR    | src.invoice_processor:process_single_invoice:201 - Failed to process G:\vscodeapps\augment\invoicekeeper\data\invoices\Noor_2025-08-23_100424.jpg: All providers failed. Last error: None
2025-08-31 14:47:08 | DEBUG    | src.invoice_processor:move_file_to_failed:354 - Moved failed file to: G:\vscodeapps\augment\invoicekeeper\data\failed\2025\08\Noor_2025-08-23_100424.jpg
2025-08-31 14:47:08 | INFO     | src.invoice_processor:process_single_invoice:206 - Moved failed file to: G:\vscodeapps\augment\invoicekeeper\data\failed\2025\08\Noor_2025-08-23_100424.jpg
2025-08-31 14:47:12 | SUCCESS  | src.ai_manager:process_invoice:76 - Successfully processed with deepseek
2025-08-31 14:47:12 | SUCCESS  | src.invoice_processor:process_single_invoice:177 - Successfully processed G:\vscodeapps\augment\invoicekeeper\data\invoices\NESTO_2025-08-31_141735.jpg in 24.83s
2025-08-31 14:47:12 | DEBUG    | src.invoice_processor:move_file_to_processed:319 - Moved successful file to: G:\vscodeapps\augment\invoicekeeper\data\processed\2025\08\NESTO_2025-08-31_141735.jpg
2025-08-31 14:47:12 | INFO     | src.invoice_processor:process_single_invoice:182 - Moved successful file to: G:\vscodeapps\augment\invoicekeeper\data\processed\2025\08\NESTO_2025-08-31_141735.jpg
2025-08-31 14:47:16 | SUCCESS  | src.ai_manager:process_invoice:76 - Successfully processed with deepseek
2025-08-31 14:47:16 | SUCCESS  | src.invoice_processor:process_single_invoice:177 - Successfully processed G:\vscodeapps\augment\invoicekeeper\data\invoices\Temu.png in 24.74s
2025-08-31 14:47:16 | DEBUG    | src.invoice_processor:move_file_to_processed:319 - Moved successful file to: G:\vscodeapps\augment\invoicekeeper\data\processed\2025\08\Temu.png
2025-08-31 14:47:16 | INFO     | src.invoice_processor:process_single_invoice:182 - Moved successful file to: G:\vscodeapps\augment\invoicekeeper\data\processed\2025\08\Temu.png
2025-08-31 14:47:18 | SUCCESS  | src.ai_manager:process_invoice:76 - Successfully processed with deepseek
2025-08-31 14:47:18 | SUCCESS  | src.invoice_processor:process_single_invoice:177 - Successfully processed G:\vscodeapps\augment\invoicekeeper\data\invoices\VIVA_20250810160802_1.jpg in 25.19s
2025-08-31 14:47:18 | DEBUG    | src.invoice_processor:move_file_to_processed:319 - Moved successful file to: G:\vscodeapps\augment\invoicekeeper\data\processed\2025\08\VIVA_20250810160802_1.jpg
2025-08-31 14:47:18 | INFO     | src.invoice_processor:process_single_invoice:182 - Moved successful file to: G:\vscodeapps\augment\invoicekeeper\data\processed\2025\08\VIVA_20250810160802_1.jpg
2025-08-31 14:47:18 | INFO     | src.invoice_processor:process_batch:259 - Batch processing completed: 4/5 successful
2025-08-31 14:47:18 | WARNING  | src.utils:convert_currency:310 - No exchange rate available for KWD to AED
2025-08-31 14:47:18 | SUCCESS  | src.excel_manager:save_invoice_data:82 - Invoice data saved to: G:\vscodeapps\augment\invoicekeeper\data\output\Invoices_2025.xlsx
2025-08-31 14:47:18 | WARNING  | src.utils:convert_currency:310 - No exchange rate available for KWD to AED
2025-08-31 15:07:45 | INFO     | src.logging_config:setup_logging:73 - Logging system initialized
2025-08-31 15:07:50 | INFO     | src.ai_manager:__init__:35 - AI Manager initialized with primary: openrouter
2025-08-31 15:07:50 | INFO     | src.ai_manager:__init__:36 - Available providers: ['google', 'deepseek', 'openrouter']
2025-08-31 15:07:50 | INFO     | src.invoice_processor:__init__:78 - Invoice Processor initialized
2025-08-31 15:07:50 | INFO     | src.excel_manager:__init__:40 - Excel Manager initialized
2025-08-31 15:07:54 | INFO     | src.ai_manager:__init__:35 - AI Manager initialized with primary: openrouter
2025-08-31 15:07:54 | INFO     | src.ai_manager:__init__:36 - Available providers: ['google', 'deepseek', 'openrouter']
2025-08-31 15:07:54 | INFO     | src.invoice_processor:discover_invoice_files:289 - Discovered 25 invoice files in G:\vscodeapps\augment\invoicekeeper\data\invoices
2025-08-31 15:07:55 | INFO     | src.invoice_processor:process_batch:232 - Starting batch processing of 5 files
2025-08-31 15:07:55 | INFO     | src.invoice_processor:process_single_invoice:100 - Processing invoice: G:\vscodeapps\augment\invoicekeeper\data\invoices\Amber 122.jpg
2025-08-31 15:07:56 | DEBUG    | src.utils:enhance_image_pil:104 - Enhanced image saved to: G:\vscodeapps\augment\invoicekeeper\data\processed\enhanced_Amber 122.jpg
2025-08-31 15:07:56 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: openrouter
2025-08-31 15:07:56 | INFO     | src.invoice_processor:process_single_invoice:100 - Processing invoice: G:\vscodeapps\augment\invoicekeeper\data\invoices\Amber 8.jpg
2025-08-31 15:07:56 | DEBUG    | src.utils:enhance_image_pil:104 - Enhanced image saved to: G:\vscodeapps\augment\invoicekeeper\data\processed\enhanced_Amber 8.jpg
2025-08-31 15:07:56 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: openrouter
2025-08-31 15:07:57 | INFO     | src.invoice_processor:process_single_invoice:100 - Processing invoice: G:\vscodeapps\augment\invoicekeeper\data\invoices\Amber_2025-08-18_133106.jpg
2025-08-31 15:07:57 | DEBUG    | src.utils:enhance_image_pil:104 - Enhanced image saved to: G:\vscodeapps\augment\invoicekeeper\data\processed\enhanced_Amber_2025-08-18_133106.jpg
2025-08-31 15:07:57 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: openrouter
2025-08-31 15:07:57 | INFO     | src.invoice_processor:process_single_invoice:100 - Processing invoice: G:\vscodeapps\augment\invoicekeeper\data\invoices\Amber_2025-08-29_13472_122.jpg
2025-08-31 15:07:58 | DEBUG    | src.utils:enhance_image_pil:104 - Enhanced image saved to: G:\vscodeapps\augment\invoicekeeper\data\processed\enhanced_Amber_2025-08-29_13472_122.jpg
2025-08-31 15:07:58 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: openrouter
2025-08-31 15:07:58 | INFO     | src.invoice_processor:process_single_invoice:100 - Processing invoice: G:\vscodeapps\augment\invoicekeeper\data\invoices\Amber_2025-08-31_134311_244.jpg
2025-08-31 15:07:58 | DEBUG    | src.utils:enhance_image_pil:104 - Enhanced image saved to: G:\vscodeapps\augment\invoicekeeper\data\processed\enhanced_Amber_2025-08-31_134311_244.jpg
2025-08-31 15:07:58 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: openrouter
2025-08-31 15:08:05 | SUCCESS  | src.ai_manager:process_invoice:76 - Successfully processed with openrouter
2025-08-31 15:08:05 | SUCCESS  | src.invoice_processor:process_single_invoice:177 - Successfully processed G:\vscodeapps\augment\invoicekeeper\data\invoices\Amber_2025-08-29_13472_122.jpg in 7.80s
2025-08-31 15:08:05 | DEBUG    | src.invoice_processor:move_file_to_processed:319 - Moved successful file to: G:\vscodeapps\augment\invoicekeeper\data\processed\2025\08\Amber_2025-08-29_13472_122.jpg
2025-08-31 15:08:05 | INFO     | src.invoice_processor:process_single_invoice:182 - Moved successful file to: G:\vscodeapps\augment\invoicekeeper\data\processed\2025\08\Amber_2025-08-29_13472_122.jpg
2025-08-31 15:08:05 | SUCCESS  | src.ai_manager:process_invoice:76 - Successfully processed with openrouter
2025-08-31 15:08:05 | SUCCESS  | src.invoice_processor:process_single_invoice:177 - Successfully processed G:\vscodeapps\augment\invoicekeeper\data\invoices\Amber 122.jpg in 9.86s
2025-08-31 15:08:05 | DEBUG    | src.invoice_processor:move_file_to_processed:319 - Moved successful file to: G:\vscodeapps\augment\invoicekeeper\data\processed\2025\08\Amber 122.jpg
2025-08-31 15:08:05 | INFO     | src.invoice_processor:process_single_invoice:182 - Moved successful file to: G:\vscodeapps\augment\invoicekeeper\data\processed\2025\08\Amber 122.jpg
2025-08-31 15:08:06 | SUCCESS  | src.ai_manager:process_invoice:76 - Successfully processed with openrouter
2025-08-31 15:08:06 | SUCCESS  | src.invoice_processor:process_single_invoice:177 - Successfully processed G:\vscodeapps\augment\invoicekeeper\data\invoices\Amber_2025-08-18_133106.jpg in 8.94s
2025-08-31 15:08:06 | DEBUG    | src.invoice_processor:move_file_to_processed:319 - Moved successful file to: G:\vscodeapps\augment\invoicekeeper\data\processed\2025\08\Amber_2025-08-18_133106.jpg
2025-08-31 15:08:06 | INFO     | src.invoice_processor:process_single_invoice:182 - Moved successful file to: G:\vscodeapps\augment\invoicekeeper\data\processed\2025\08\Amber_2025-08-18_133106.jpg
2025-08-31 15:08:06 | SUCCESS  | src.ai_manager:process_invoice:76 - Successfully processed with openrouter
2025-08-31 15:08:06 | SUCCESS  | src.invoice_processor:process_single_invoice:177 - Successfully processed G:\vscodeapps\augment\invoicekeeper\data\invoices\Amber_2025-08-31_134311_244.jpg in 7.66s
2025-08-31 15:08:06 | DEBUG    | src.invoice_processor:move_file_to_processed:319 - Moved successful file to: G:\vscodeapps\augment\invoicekeeper\data\processed\2025\08\Amber_2025-08-31_134311_244.jpg
2025-08-31 15:08:06 | INFO     | src.invoice_processor:process_single_invoice:182 - Moved successful file to: G:\vscodeapps\augment\invoicekeeper\data\processed\2025\08\Amber_2025-08-31_134311_244.jpg
2025-08-31 15:08:06 | SUCCESS  | src.ai_manager:process_invoice:76 - Successfully processed with openrouter
2025-08-31 15:08:06 | SUCCESS  | src.invoice_processor:process_single_invoice:177 - Successfully processed G:\vscodeapps\augment\invoicekeeper\data\invoices\Amber 8.jpg in 9.85s
2025-08-31 15:08:06 | DEBUG    | src.invoice_processor:move_file_to_processed:319 - Moved successful file to: G:\vscodeapps\augment\invoicekeeper\data\processed\2025\08\Amber 8.jpg
2025-08-31 15:08:06 | INFO     | src.invoice_processor:process_single_invoice:182 - Moved successful file to: G:\vscodeapps\augment\invoicekeeper\data\processed\2025\08\Amber 8.jpg
2025-08-31 15:08:06 | INFO     | src.invoice_processor:process_batch:259 - Batch processing completed: 5/5 successful
2025-08-31 15:08:06 | INFO     | src.invoice_processor:process_batch:232 - Starting batch processing of 5 files
2025-08-31 15:08:06 | INFO     | src.invoice_processor:process_single_invoice:100 - Processing invoice: G:\vscodeapps\augment\invoicekeeper\data\invoices\Amber_2025-08-31_134311_534.jpg
2025-08-31 15:08:06 | DEBUG    | src.utils:enhance_image_pil:104 - Enhanced image saved to: G:\vscodeapps\augment\invoicekeeper\data\processed\enhanced_Amber_2025-08-31_134311_534.jpg
2025-08-31 15:08:06 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: openrouter
2025-08-31 15:08:07 | INFO     | src.invoice_processor:process_single_invoice:100 - Processing invoice: G:\vscodeapps\augment\invoicekeeper\data\invoices\Amber_256.jpg
2025-08-31 15:08:07 | DEBUG    | src.utils:enhance_image_pil:104 - Enhanced image saved to: G:\vscodeapps\augment\invoicekeeper\data\processed\enhanced_Amber_256.jpg
2025-08-31 15:08:07 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: openrouter
2025-08-31 15:08:07 | INFO     | src.invoice_processor:process_single_invoice:100 - Processing invoice: G:\vscodeapps\augment\invoicekeeper\data\invoices\Amber_43.jpg
2025-08-31 15:08:08 | DEBUG    | src.utils:enhance_image_pil:104 - Enhanced image saved to: G:\vscodeapps\augment\invoicekeeper\data\processed\enhanced_Amber_43.jpg
2025-08-31 15:08:08 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: openrouter
2025-08-31 15:08:08 | INFO     | src.invoice_processor:process_single_invoice:100 - Processing invoice: G:\vscodeapps\augment\invoicekeeper\data\invoices\Calicut_20250810160800.jpg
2025-08-31 15:08:08 | DEBUG    | src.utils:enhance_image_pil:104 - Enhanced image saved to: G:\vscodeapps\augment\invoicekeeper\data\processed\enhanced_Calicut_20250810160800.jpg
2025-08-31 15:08:08 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: openrouter
2025-08-31 15:08:09 | INFO     | src.invoice_processor:process_single_invoice:100 - Processing invoice: G:\vscodeapps\augment\invoicekeeper\data\invoices\DayToDAy_15.jpg
2025-08-31 15:08:09 | DEBUG    | src.utils:enhance_image_pil:104 - Enhanced image saved to: G:\vscodeapps\augment\invoicekeeper\data\processed\enhanced_DayToDAy_15.jpg
2025-08-31 15:08:09 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: openrouter
2025-08-31 15:08:16 | SUCCESS  | src.ai_manager:process_invoice:76 - Successfully processed with openrouter
2025-08-31 15:08:16 | SUCCESS  | src.invoice_processor:process_single_invoice:177 - Successfully processed G:\vscodeapps\augment\invoicekeeper\data\invoices\Amber_256.jpg in 9.67s
2025-08-31 15:08:16 | DEBUG    | src.invoice_processor:move_file_to_processed:319 - Moved successful file to: G:\vscodeapps\augment\invoicekeeper\data\processed\2025\08\Amber_256.jpg
2025-08-31 15:08:16 | INFO     | src.invoice_processor:process_single_invoice:182 - Moved successful file to: G:\vscodeapps\augment\invoicekeeper\data\processed\2025\08\Amber_256.jpg
2025-08-31 15:08:16 | SUCCESS  | src.ai_manager:process_invoice:76 - Successfully processed with openrouter
2025-08-31 15:08:16 | SUCCESS  | src.invoice_processor:process_single_invoice:177 - Successfully processed G:\vscodeapps\augment\invoicekeeper\data\invoices\Amber_2025-08-31_134311_534.jpg in 10.55s
2025-08-31 15:08:16 | DEBUG    | src.invoice_processor:move_file_to_processed:319 - Moved successful file to: G:\vscodeapps\augment\invoicekeeper\data\processed\2025\08\Amber_2025-08-31_134311_534.jpg
2025-08-31 15:08:16 | INFO     | src.invoice_processor:process_single_invoice:182 - Moved successful file to: G:\vscodeapps\augment\invoicekeeper\data\processed\2025\08\Amber_2025-08-31_134311_534.jpg
2025-08-31 15:08:17 | SUCCESS  | src.ai_manager:process_invoice:76 - Successfully processed with openrouter
2025-08-31 15:08:17 | SUCCESS  | src.invoice_processor:process_single_invoice:177 - Successfully processed G:\vscodeapps\augment\invoicekeeper\data\invoices\Amber_43.jpg in 9.65s
2025-08-31 15:08:17 | DEBUG    | src.invoice_processor:move_file_to_processed:319 - Moved successful file to: G:\vscodeapps\augment\invoicekeeper\data\processed\2025\08\Amber_43.jpg
2025-08-31 15:08:17 | INFO     | src.invoice_processor:process_single_invoice:182 - Moved successful file to: G:\vscodeapps\augment\invoicekeeper\data\processed\2025\08\Amber_43.jpg
2025-08-31 15:08:17 | SUCCESS  | src.ai_manager:process_invoice:76 - Successfully processed with openrouter
2025-08-31 15:08:17 | SUCCESS  | src.invoice_processor:process_single_invoice:177 - Successfully processed G:\vscodeapps\augment\invoicekeeper\data\invoices\Calicut_20250810160800.jpg in 9.22s
2025-08-31 15:08:17 | DEBUG    | src.invoice_processor:move_file_to_processed:319 - Moved successful file to: G:\vscodeapps\augment\invoicekeeper\data\processed\2025\08\Calicut_20250810160800.jpg
2025-08-31 15:08:17 | INFO     | src.invoice_processor:process_single_invoice:182 - Moved successful file to: G:\vscodeapps\augment\invoicekeeper\data\processed\2025\08\Calicut_20250810160800.jpg
2025-08-31 15:08:22 | SUCCESS  | src.ai_manager:process_invoice:76 - Successfully processed with openrouter
2025-08-31 15:08:22 | SUCCESS  | src.invoice_processor:process_single_invoice:177 - Successfully processed G:\vscodeapps\augment\invoicekeeper\data\invoices\DayToDAy_15.jpg in 13.40s
2025-08-31 15:08:22 | DEBUG    | src.invoice_processor:move_file_to_processed:319 - Moved successful file to: G:\vscodeapps\augment\invoicekeeper\data\processed\2025\08\DayToDAy_15.jpg
2025-08-31 15:08:22 | INFO     | src.invoice_processor:process_single_invoice:182 - Moved successful file to: G:\vscodeapps\augment\invoicekeeper\data\processed\2025\08\DayToDAy_15.jpg
2025-08-31 15:08:22 | INFO     | src.invoice_processor:process_batch:259 - Batch processing completed: 5/5 successful
2025-08-31 15:08:22 | INFO     | src.invoice_processor:process_batch:232 - Starting batch processing of 5 files
2025-08-31 15:08:22 | INFO     | src.invoice_processor:process_single_invoice:100 - Processing invoice: G:\vscodeapps\augment\invoicekeeper\data\invoices\DayToDAy_17.jpg
2025-08-31 15:08:22 | DEBUG    | src.utils:enhance_image_pil:104 - Enhanced image saved to: G:\vscodeapps\augment\invoicekeeper\data\processed\enhanced_DayToDAy_17.jpg
2025-08-31 15:08:22 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: openrouter
2025-08-31 15:08:23 | INFO     | src.invoice_processor:process_single_invoice:100 - Processing invoice: G:\vscodeapps\augment\invoicekeeper\data\invoices\DayToDAy_2025-08-17_132754.jpg
2025-08-31 15:08:23 | DEBUG    | src.utils:enhance_image_pil:104 - Enhanced image saved to: G:\vscodeapps\augment\invoicekeeper\data\processed\enhanced_DayToDAy_2025-08-17_132754.jpg
2025-08-31 15:08:23 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: openrouter
2025-08-31 15:08:24 | INFO     | src.invoice_processor:process_single_invoice:100 - Processing invoice: G:\vscodeapps\augment\invoicekeeper\data\invoices\DayToDay_2025-08-18_133256.jpg
2025-08-31 15:08:24 | DEBUG    | src.utils:enhance_image_pil:104 - Enhanced image saved to: G:\vscodeapps\augment\invoicekeeper\data\processed\enhanced_DayToDay_2025-08-18_133256.jpg
2025-08-31 15:08:24 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: openrouter
2025-08-31 15:08:24 | INFO     | src.invoice_processor:process_single_invoice:100 - Processing invoice: G:\vscodeapps\augment\invoicekeeper\data\invoices\DayToDAy_83.jpg
2025-08-31 15:08:24 | DEBUG    | src.utils:enhance_image_pil:104 - Enhanced image saved to: G:\vscodeapps\augment\invoicekeeper\data\processed\enhanced_DayToDAy_83.jpg
2025-08-31 15:08:24 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: openrouter
2025-08-31 15:08:25 | INFO     | src.invoice_processor:process_single_invoice:100 - Processing invoice: G:\vscodeapps\augment\invoicekeeper\data\invoices\Fancy_2025-08-29_134729.jpg
2025-08-31 15:08:25 | DEBUG    | src.utils:enhance_image_pil:104 - Enhanced image saved to: G:\vscodeapps\augment\invoicekeeper\data\processed\enhanced_Fancy_2025-08-29_134729.jpg
2025-08-31 15:08:25 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: openrouter
2025-08-31 15:08:32 | SUCCESS  | src.ai_manager:process_invoice:76 - Successfully processed with openrouter
2025-08-31 15:08:32 | SUCCESS  | src.invoice_processor:process_single_invoice:177 - Successfully processed G:\vscodeapps\augment\invoicekeeper\data\invoices\DayToDAy_17.jpg in 10.32s
2025-08-31 15:08:32 | DEBUG    | src.invoice_processor:move_file_to_processed:319 - Moved successful file to: G:\vscodeapps\augment\invoicekeeper\data\processed\2025\08\DayToDAy_17.jpg
2025-08-31 15:08:32 | INFO     | src.invoice_processor:process_single_invoice:182 - Moved successful file to: G:\vscodeapps\augment\invoicekeeper\data\processed\2025\08\DayToDAy_17.jpg
2025-08-31 15:08:33 | SUCCESS  | src.ai_manager:process_invoice:76 - Successfully processed with openrouter
2025-08-31 15:08:33 | SUCCESS  | src.invoice_processor:process_single_invoice:177 - Successfully processed G:\vscodeapps\augment\invoicekeeper\data\invoices\DayToDAy_2025-08-17_132754.jpg in 9.77s
2025-08-31 15:08:33 | DEBUG    | src.invoice_processor:move_file_to_processed:319 - Moved successful file to: G:\vscodeapps\augment\invoicekeeper\data\processed\2025\08\DayToDAy_2025-08-17_132754.jpg
2025-08-31 15:08:33 | INFO     | src.invoice_processor:process_single_invoice:182 - Moved successful file to: G:\vscodeapps\augment\invoicekeeper\data\processed\2025\08\DayToDAy_2025-08-17_132754.jpg
2025-08-31 15:08:33 | SUCCESS  | src.ai_manager:process_invoice:76 - Successfully processed with openrouter
2025-08-31 15:08:33 | SUCCESS  | src.invoice_processor:process_single_invoice:177 - Successfully processed G:\vscodeapps\augment\invoicekeeper\data\invoices\Fancy_2025-08-29_134729.jpg in 7.85s
2025-08-31 15:08:33 | DEBUG    | src.invoice_processor:move_file_to_processed:319 - Moved successful file to: G:\vscodeapps\augment\invoicekeeper\data\processed\2025\08\Fancy_2025-08-29_134729.jpg
2025-08-31 15:08:33 | INFO     | src.invoice_processor:process_single_invoice:182 - Moved successful file to: G:\vscodeapps\augment\invoicekeeper\data\processed\2025\08\Fancy_2025-08-29_134729.jpg
2025-08-31 15:08:33 | SUCCESS  | src.ai_manager:process_invoice:76 - Successfully processed with openrouter
2025-08-31 15:08:33 | SUCCESS  | src.invoice_processor:process_single_invoice:177 - Successfully processed G:\vscodeapps\augment\invoicekeeper\data\invoices\DayToDay_2025-08-18_133256.jpg in 9.37s
2025-08-31 15:08:33 | DEBUG    | src.invoice_processor:move_file_to_processed:319 - Moved successful file to: G:\vscodeapps\augment\invoicekeeper\data\processed\2025\08\DayToDay_2025-08-18_133256.jpg
2025-08-31 15:08:33 | INFO     | src.invoice_processor:process_single_invoice:182 - Moved successful file to: G:\vscodeapps\augment\invoicekeeper\data\processed\2025\08\DayToDay_2025-08-18_133256.jpg
2025-08-31 15:08:34 | SUCCESS  | src.ai_manager:process_invoice:76 - Successfully processed with openrouter
2025-08-31 15:08:34 | SUCCESS  | src.invoice_processor:process_single_invoice:177 - Successfully processed G:\vscodeapps\augment\invoicekeeper\data\invoices\DayToDAy_83.jpg in 9.37s
2025-08-31 15:08:34 | DEBUG    | src.invoice_processor:move_file_to_processed:319 - Moved successful file to: G:\vscodeapps\augment\invoicekeeper\data\processed\2025\08\DayToDAy_83.jpg
2025-08-31 15:08:34 | INFO     | src.invoice_processor:process_single_invoice:182 - Moved successful file to: G:\vscodeapps\augment\invoicekeeper\data\processed\2025\08\DayToDAy_83.jpg
2025-08-31 15:08:34 | INFO     | src.invoice_processor:process_batch:259 - Batch processing completed: 5/5 successful
2025-08-31 15:08:34 | INFO     | src.invoice_processor:process_batch:232 - Starting batch processing of 5 files
2025-08-31 15:08:34 | INFO     | src.invoice_processor:process_single_invoice:100 - Processing invoice: G:\vscodeapps\augment\invoicekeeper\data\invoices\Invoice-1.png
2025-08-31 15:08:34 | DEBUG    | src.utils:enhance_image_pil:104 - Enhanced image saved to: G:\vscodeapps\augment\invoicekeeper\data\processed\enhanced_Invoice-1.png
2025-08-31 15:08:34 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: openrouter
2025-08-31 15:08:34 | INFO     | src.invoice_processor:process_single_invoice:100 - Processing invoice: G:\vscodeapps\augment\invoicekeeper\data\invoices\Invoice-2.jpg
2025-08-31 15:08:35 | DEBUG    | src.utils:enhance_image_pil:104 - Enhanced image saved to: G:\vscodeapps\augment\invoicekeeper\data\processed\enhanced_Invoice-2.jpg
2025-08-31 15:08:35 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: openrouter
2025-08-31 15:08:36 | INFO     | src.invoice_processor:process_single_invoice:100 - Processing invoice: G:\vscodeapps\augment\invoicekeeper\data\invoices\Invoice-4.jpg
2025-08-31 15:08:36 | DEBUG    | src.utils:enhance_image_pil:104 - Enhanced image saved to: G:\vscodeapps\augment\invoicekeeper\data\processed\enhanced_Invoice-4.jpg
2025-08-31 15:08:36 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: openrouter
2025-08-31 15:08:37 | INFO     | src.invoice_processor:process_single_invoice:100 - Processing invoice: G:\vscodeapps\augment\invoicekeeper\data\invoices\Invoice_2025-08-14_130400.jpg
2025-08-31 15:08:37 | DEBUG    | src.utils:enhance_image_pil:104 - Enhanced image saved to: G:\vscodeapps\augment\invoicekeeper\data\processed\enhanced_Invoice_2025-08-14_130400.jpg
2025-08-31 15:08:37 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: openrouter
2025-08-31 15:08:37 | INFO     | src.invoice_processor:process_single_invoice:100 - Processing invoice: G:\vscodeapps\augment\invoicekeeper\data\invoices\NESTO_2025-08-31_141544.jpg
2025-08-31 15:08:38 | DEBUG    | src.utils:enhance_image_pil:104 - Enhanced image saved to: G:\vscodeapps\augment\invoicekeeper\data\processed\enhanced_NESTO_2025-08-31_141544.jpg
2025-08-31 15:08:38 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: openrouter
2025-08-31 15:08:44 | SUCCESS  | src.ai_manager:process_invoice:76 - Successfully processed with openrouter
2025-08-31 15:08:44 | SUCCESS  | src.invoice_processor:process_single_invoice:177 - Successfully processed G:\vscodeapps\augment\invoicekeeper\data\invoices\Invoice-1.png in 10.14s
2025-08-31 15:08:44 | DEBUG    | src.invoice_processor:move_file_to_processed:319 - Moved successful file to: G:\vscodeapps\augment\invoicekeeper\data\processed\2025\08\Invoice-1.png
2025-08-31 15:08:44 | INFO     | src.invoice_processor:process_single_invoice:182 - Moved successful file to: G:\vscodeapps\augment\invoicekeeper\data\processed\2025\08\Invoice-1.png
2025-08-31 15:08:44 | SUCCESS  | src.ai_manager:process_invoice:76 - Successfully processed with openrouter
2025-08-31 15:08:44 | SUCCESS  | src.invoice_processor:process_single_invoice:177 - Successfully processed G:\vscodeapps\augment\invoicekeeper\data\invoices\NESTO_2025-08-31_141544.jpg in 7.19s
2025-08-31 15:08:44 | DEBUG    | src.invoice_processor:move_file_to_processed:319 - Moved successful file to: G:\vscodeapps\augment\invoicekeeper\data\processed\2025\08\NESTO_2025-08-31_141544.jpg
2025-08-31 15:08:44 | INFO     | src.invoice_processor:process_single_invoice:182 - Moved successful file to: G:\vscodeapps\augment\invoicekeeper\data\processed\2025\08\NESTO_2025-08-31_141544.jpg
2025-08-31 15:08:46 | SUCCESS  | src.ai_manager:process_invoice:76 - Successfully processed with openrouter
2025-08-31 15:08:46 | SUCCESS  | src.invoice_processor:process_single_invoice:177 - Successfully processed G:\vscodeapps\augment\invoicekeeper\data\invoices\Invoice-4.jpg in 9.99s
2025-08-31 15:08:46 | DEBUG    | src.invoice_processor:move_file_to_processed:319 - Moved successful file to: G:\vscodeapps\augment\invoicekeeper\data\processed\2025\08\Invoice-4.jpg
2025-08-31 15:08:46 | INFO     | src.invoice_processor:process_single_invoice:182 - Moved successful file to: G:\vscodeapps\augment\invoicekeeper\data\processed\2025\08\Invoice-4.jpg
2025-08-31 15:08:46 | SUCCESS  | src.ai_manager:process_invoice:76 - Successfully processed with openrouter
2025-08-31 15:08:46 | SUCCESS  | src.invoice_processor:process_single_invoice:177 - Successfully processed G:\vscodeapps\augment\invoicekeeper\data\invoices\Invoice_2025-08-14_130400.jpg in 9.60s
2025-08-31 15:08:46 | DEBUG    | src.invoice_processor:move_file_to_processed:319 - Moved successful file to: G:\vscodeapps\augment\invoicekeeper\data\processed\2025\08\Invoice_2025-08-14_130400.jpg
2025-08-31 15:08:46 | INFO     | src.invoice_processor:process_single_invoice:182 - Moved successful file to: G:\vscodeapps\augment\invoicekeeper\data\processed\2025\08\Invoice_2025-08-14_130400.jpg
2025-08-31 15:08:49 | SUCCESS  | src.ai_manager:process_invoice:76 - Successfully processed with openrouter
2025-08-31 15:08:49 | SUCCESS  | src.invoice_processor:process_single_invoice:177 - Successfully processed G:\vscodeapps\augment\invoicekeeper\data\invoices\Invoice-2.jpg in 15.15s
2025-08-31 15:08:49 | DEBUG    | src.invoice_processor:move_file_to_processed:319 - Moved successful file to: G:\vscodeapps\augment\invoicekeeper\data\processed\2025\08\Invoice-2.jpg
2025-08-31 15:08:49 | INFO     | src.invoice_processor:process_single_invoice:182 - Moved successful file to: G:\vscodeapps\augment\invoicekeeper\data\processed\2025\08\Invoice-2.jpg
2025-08-31 15:08:49 | INFO     | src.invoice_processor:process_batch:259 - Batch processing completed: 5/5 successful
2025-08-31 15:08:49 | INFO     | src.invoice_processor:process_batch:232 - Starting batch processing of 5 files
2025-08-31 15:08:49 | INFO     | src.invoice_processor:process_single_invoice:100 - Processing invoice: G:\vscodeapps\augment\invoicekeeper\data\invoices\NESTO_2025-08-31_141735.jpg
2025-08-31 15:08:50 | DEBUG    | src.utils:enhance_image_pil:104 - Enhanced image saved to: G:\vscodeapps\augment\invoicekeeper\data\processed\enhanced_NESTO_2025-08-31_141735.jpg
2025-08-31 15:08:50 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: openrouter
2025-08-31 15:08:50 | INFO     | src.invoice_processor:process_single_invoice:100 - Processing invoice: G:\vscodeapps\augment\invoicekeeper\data\invoices\Noor_2025-08-23_100424.jpg
2025-08-31 15:08:50 | DEBUG    | src.utils:enhance_image_pil:104 - Enhanced image saved to: G:\vscodeapps\augment\invoicekeeper\data\processed\enhanced_Noor_2025-08-23_100424.jpg
2025-08-31 15:08:50 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: openrouter
2025-08-31 15:08:51 | INFO     | src.invoice_processor:process_single_invoice:100 - Processing invoice: G:\vscodeapps\augment\invoicekeeper\data\invoices\Screenshot 2025-07-15 120050.png
2025-08-31 15:08:51 | DEBUG    | src.utils:enhance_image_pil:104 - Enhanced image saved to: G:\vscodeapps\augment\invoicekeeper\data\processed\enhanced_Screenshot 2025-07-15 120050.png
2025-08-31 15:08:51 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: openrouter
2025-08-31 15:08:51 | INFO     | src.invoice_processor:process_single_invoice:100 - Processing invoice: G:\vscodeapps\augment\invoicekeeper\data\invoices\Temu.png
2025-08-31 15:08:52 | DEBUG    | src.utils:enhance_image_pil:104 - Enhanced image saved to: G:\vscodeapps\augment\invoicekeeper\data\processed\enhanced_Temu.png
2025-08-31 15:08:52 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: openrouter
2025-08-31 15:08:52 | INFO     | src.invoice_processor:process_single_invoice:100 - Processing invoice: G:\vscodeapps\augment\invoicekeeper\data\invoices\VIVA_20250810160802_1.jpg
2025-08-31 15:08:52 | DEBUG    | src.utils:enhance_image_pil:104 - Enhanced image saved to: G:\vscodeapps\augment\invoicekeeper\data\processed\enhanced_VIVA_20250810160802_1.jpg
2025-08-31 15:08:52 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: openrouter
2025-08-31 15:08:57 | SUCCESS  | src.ai_manager:process_invoice:76 - Successfully processed with openrouter
2025-08-31 15:08:57 | SUCCESS  | src.invoice_processor:process_single_invoice:177 - Successfully processed G:\vscodeapps\augment\invoicekeeper\data\invoices\Screenshot 2025-07-15 120050.png in 6.06s
2025-08-31 15:08:57 | DEBUG    | src.invoice_processor:move_file_to_processed:319 - Moved successful file to: G:\vscodeapps\augment\invoicekeeper\data\processed\2025\08\Screenshot 2025-07-15 120050.png
2025-08-31 15:08:57 | INFO     | src.invoice_processor:process_single_invoice:182 - Moved successful file to: G:\vscodeapps\augment\invoicekeeper\data\processed\2025\08\Screenshot 2025-07-15 120050.png
2025-08-31 15:08:58 | SUCCESS  | src.ai_manager:process_invoice:76 - Successfully processed with openrouter
2025-08-31 15:08:58 | SUCCESS  | src.invoice_processor:process_single_invoice:177 - Successfully processed G:\vscodeapps\augment\invoicekeeper\data\invoices\Noor_2025-08-23_100424.jpg in 8.34s
2025-08-31 15:08:58 | DEBUG    | src.invoice_processor:move_file_to_processed:319 - Moved successful file to: G:\vscodeapps\augment\invoicekeeper\data\processed\2025\08\Noor_2025-08-23_100424.jpg
2025-08-31 15:08:58 | INFO     | src.invoice_processor:process_single_invoice:182 - Moved successful file to: G:\vscodeapps\augment\invoicekeeper\data\processed\2025\08\Noor_2025-08-23_100424.jpg
2025-08-31 15:09:01 | SUCCESS  | src.ai_manager:process_invoice:76 - Successfully processed with openrouter
2025-08-31 15:09:01 | SUCCESS  | src.invoice_processor:process_single_invoice:177 - Successfully processed G:\vscodeapps\augment\invoicekeeper\data\invoices\NESTO_2025-08-31_141735.jpg in 11.17s
2025-08-31 15:09:01 | DEBUG    | src.invoice_processor:move_file_to_processed:319 - Moved successful file to: G:\vscodeapps\augment\invoicekeeper\data\processed\2025\08\NESTO_2025-08-31_141735.jpg
2025-08-31 15:09:01 | INFO     | src.invoice_processor:process_single_invoice:182 - Moved successful file to: G:\vscodeapps\augment\invoicekeeper\data\processed\2025\08\NESTO_2025-08-31_141735.jpg
2025-08-31 15:09:01 | SUCCESS  | src.ai_manager:process_invoice:76 - Successfully processed with openrouter
2025-08-31 15:09:01 | SUCCESS  | src.invoice_processor:process_single_invoice:177 - Successfully processed G:\vscodeapps\augment\invoicekeeper\data\invoices\VIVA_20250810160802_1.jpg in 8.53s
2025-08-31 15:09:01 | DEBUG    | src.invoice_processor:move_file_to_processed:319 - Moved successful file to: G:\vscodeapps\augment\invoicekeeper\data\processed\2025\08\VIVA_20250810160802_1.jpg
2025-08-31 15:09:01 | INFO     | src.invoice_processor:process_single_invoice:182 - Moved successful file to: G:\vscodeapps\augment\invoicekeeper\data\processed\2025\08\VIVA_20250810160802_1.jpg
2025-08-31 15:09:01 | SUCCESS  | src.ai_manager:process_invoice:76 - Successfully processed with openrouter
2025-08-31 15:09:01 | SUCCESS  | src.invoice_processor:process_single_invoice:177 - Successfully processed G:\vscodeapps\augment\invoicekeeper\data\invoices\Temu.png in 9.46s
2025-08-31 15:09:01 | DEBUG    | src.invoice_processor:move_file_to_processed:319 - Moved successful file to: G:\vscodeapps\augment\invoicekeeper\data\processed\2025\08\Temu.png
2025-08-31 15:09:01 | INFO     | src.invoice_processor:process_single_invoice:182 - Moved successful file to: G:\vscodeapps\augment\invoicekeeper\data\processed\2025\08\Temu.png
2025-08-31 15:09:01 | INFO     | src.invoice_processor:process_batch:259 - Batch processing completed: 5/5 successful
2025-08-31 15:09:01 | SUCCESS  | src.excel_manager:save_invoice_data:82 - Invoice data saved to: G:\vscodeapps\augment\invoicekeeper\data\output\Invoices_2025.xlsx
2025-09-01 13:35:21 | INFO     | src.logging_config:setup_logging:73 - Logging system initialized
2025-09-01 13:35:26 | INFO     | src.ai_manager:__init__:35 - AI Manager initialized with primary: openrouter
2025-09-01 13:35:26 | INFO     | src.ai_manager:__init__:36 - Available providers: ['google', 'deepseek', 'openrouter']
2025-09-01 13:35:26 | INFO     | src.invoice_processor:__init__:78 - Invoice Processor initialized
2025-09-01 13:35:26 | INFO     | src.excel_manager:__init__:40 - Excel Manager initialized
2025-09-01 13:35:30 | INFO     | src.ai_manager:__init__:35 - AI Manager initialized with primary: openrouter
2025-09-01 13:35:30 | INFO     | src.ai_manager:__init__:36 - Available providers: ['google', 'deepseek', 'openrouter']
2025-09-01 13:35:30 | INFO     | src.invoice_processor:discover_invoice_files:289 - Discovered 2 invoice files in G:\vscodeapps\augment\invoicekeeper\data\invoices
2025-09-01 13:35:32 | INFO     | src.invoice_processor:process_batch:232 - Starting batch processing of 2 files
2025-09-01 13:35:32 | INFO     | src.invoice_processor:process_single_invoice:100 - Processing invoice: G:\vscodeapps\augment\invoicekeeper\data\invoices\Smile Tea Cafeteria Invoice-01.png
2025-09-01 13:35:32 | DEBUG    | src.utils:enhance_image_pil:104 - Enhanced image saved to: G:\vscodeapps\augment\invoicekeeper\data\processed\enhanced_Smile Tea Cafeteria Invoice-01.png
2025-09-01 13:35:32 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: openrouter
2025-09-01 13:35:33 | INFO     | src.invoice_processor:process_single_invoice:100 - Processing invoice: G:\vscodeapps\augment\invoicekeeper\data\invoices\TASTY RESTAURANT Invoice 0901-01.png
2025-09-01 13:35:33 | DEBUG    | src.utils:enhance_image_pil:104 - Enhanced image saved to: G:\vscodeapps\augment\invoicekeeper\data\processed\enhanced_TASTY RESTAURANT Invoice 0901-01.png
2025-09-01 13:35:33 | INFO     | src.ai_manager:process_invoice:72 - Attempting to process with provider: openrouter
2025-09-01 13:35:39 | SUCCESS  | src.ai_manager:process_invoice:76 - Successfully processed with openrouter
2025-09-01 13:35:39 | SUCCESS  | src.invoice_processor:process_single_invoice:177 - Successfully processed G:\vscodeapps\augment\invoicekeeper\data\invoices\TASTY RESTAURANT Invoice 0901-01.png in 5.96s
2025-09-01 13:35:39 | DEBUG    | src.invoice_processor:move_file_to_processed:319 - Moved successful file to: G:\vscodeapps\augment\invoicekeeper\data\processed\2025\09\TASTY RESTAURANT Invoice 0901-01.png
2025-09-01 13:35:39 | INFO     | src.invoice_processor:process_single_invoice:182 - Moved successful file to: G:\vscodeapps\augment\invoicekeeper\data\processed\2025\09\TASTY RESTAURANT Invoice 0901-01.png
2025-09-01 13:35:40 | SUCCESS  | src.ai_manager:process_invoice:76 - Successfully processed with openrouter
2025-09-01 13:35:40 | SUCCESS  | src.invoice_processor:process_single_invoice:177 - Successfully processed G:\vscodeapps\augment\invoicekeeper\data\invoices\Smile Tea Cafeteria Invoice-01.png in 8.01s
2025-09-01 13:35:40 | DEBUG    | src.invoice_processor:move_file_to_processed:319 - Moved successful file to: G:\vscodeapps\augment\invoicekeeper\data\processed\2025\09\Smile Tea Cafeteria Invoice-01.png
2025-09-01 13:35:40 | INFO     | src.invoice_processor:process_single_invoice:182 - Moved successful file to: G:\vscodeapps\augment\invoicekeeper\data\processed\2025\09\Smile Tea Cafeteria Invoice-01.png
2025-09-01 13:35:40 | INFO     | src.invoice_processor:process_batch:259 - Batch processing completed: 2/2 successful
2025-09-01 13:35:40 | INFO     | src.excel_manager:_remove_duplicate_invoices:641 - Removed 3 duplicate entries
2025-09-01 13:35:40 | SUCCESS  | src.excel_manager:save_invoice_data:82 - Invoice data saved to: G:\vscodeapps\augment\invoicekeeper\data\output\Invoices_2025.xlsx
