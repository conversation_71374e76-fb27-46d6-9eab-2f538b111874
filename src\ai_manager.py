"""
AI Provider Management System for Invoice Processing.
"""

import json
import base64
import asyncio
from typing import Dict, List, Optional, Any, Union
from pathlib import Path
from PIL import Image
import httpx
from loguru import logger

from config.settings import settings
from config.ai_providers import (
    PROVIDER_CONFIGS, 
    get_provider_config, 
    get_extraction_prompt,
    AIProvider
)

class AIProviderError(Exception):
    """Custom exception for AI provider errors."""
    pass

class AIManager:
    """Manages multiple AI providers with fallback support."""
    
    def __init__(self):
        self.primary_provider = settings.default_ai_provider
        self.fallback_providers = settings.fallback_providers_list
        self.available_providers = settings.get_available_providers()
        self.client_cache: Dict[str, Any] = {}

        logger.info(f"AI Manager initialized with primary: {self.primary_provider}")
        logger.info(f"Available providers: {self.available_providers}")
    
    async def process_invoice(
        self, 
        image_path: Path, 
        provider: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Process an invoice image using AI providers with fallback.
        
        Args:
            image_path: Path to the invoice image
            provider: Specific provider to use (optional)
            
        Returns:
            Extracted invoice data as dictionary
        """
        providers_to_try = [provider] if provider else [self.primary_provider] + self.fallback_providers
        providers_to_try = [p for p in providers_to_try if p in self.available_providers]

        # Filter out providers that don't support vision for image processing
        vision_capable_providers = []
        for p in providers_to_try:
            config = get_provider_config(p)
            if config and config.vision_capable:
                vision_capable_providers.append(p)

        providers_to_try = vision_capable_providers
        
        if not providers_to_try:
            raise AIProviderError("No available AI providers configured")
        
        last_error = None
        
        for provider_name in providers_to_try:
            try:
                logger.info(f"Attempting to process with provider: {provider_name}")
                result = await self._process_with_provider(image_path, provider_name)
                
                if result and result.get("confidence", 0) >= settings.confidence_threshold:
                    logger.success(f"Successfully processed with {provider_name}")
                    result["provider_used"] = provider_name
                    return result
                else:
                    logger.warning(f"Low confidence result from {provider_name}")
                    
            except Exception as e:
                logger.error(f"Error with provider {provider_name}: {str(e)}")
                last_error = e
                continue
        
        # If all providers failed, raise the last error
        raise AIProviderError(f"All providers failed. Last error: {str(last_error)}")
    
    async def _process_with_provider(
        self, 
        image_path: Path, 
        provider: str
    ) -> Dict[str, Any]:
        """Process invoice with a specific provider."""
        
        if provider == "openai":
            return await self._process_openai(image_path)
        elif provider == "anthropic":
            return await self._process_anthropic(image_path)
        elif provider == "google":
            return await self._process_google(image_path)
        elif provider == "deepseek":
            return await self._process_deepseek(image_path)
        elif provider == "openrouter":
            return await self._process_openrouter(image_path)
        elif provider == "mistral":
            return await self._process_mistral(image_path)
        elif provider == "ollama":
            return await self._process_ollama(image_path)
        elif provider == "custom":
            return await self._process_custom(image_path)
        else:
            raise AIProviderError(f"Unsupported provider: {provider}")
    
    def _encode_image(self, image_path: Path) -> str:
        """Encode image to base64."""
        with open(image_path, "rb") as image_file:
            return base64.b64encode(image_file.read()).decode('utf-8')
    
    async def _process_deepseek(self, image_path: Path) -> Dict[str, Any]:
        """Process with DeepSeek API using OpenAI SDK format."""
        try:
            import openai
        except ImportError:
            raise AIProviderError("OpenAI library not installed")

        api_key = settings.get_api_key("deepseek")
        if not api_key:
            raise AIProviderError("DeepSeek API key not configured")

        # DeepSeek doesn't support vision, so we'll use OCR + text processing
        try:
            import pytesseract
            from PIL import Image

            # Extract text using OCR
            image = Image.open(image_path)
            extracted_text = pytesseract.image_to_string(image)

            if not extracted_text.strip():
                raise AIProviderError("No text could be extracted from the image")

            # Use DeepSeek for text-based invoice processing
            client = openai.AsyncOpenAI(
                api_key=api_key,
                base_url="https://api.deepseek.com"
            )

            prompts = get_extraction_prompt("deepseek")

            # Modified prompt for text-based processing
            text_prompt = f"""
{prompts["system"]}

Here is the text extracted from an invoice image using OCR:

{extracted_text}

Please extract the invoice data and return it in the specified JSON format.
"""

            response = await client.chat.completions.create(
                model="deepseek-chat",
                messages=[
                    {"role": "system", "content": prompts["system"]},
                    {"role": "user", "content": text_prompt}
                ],
                max_tokens=4000,
                temperature=0.1
            )

            content = response.choices[0].message.content

            # Parse JSON response
            try:
                return json.loads(content)
            except json.JSONDecodeError:
                # Try to extract JSON from the response
                import re
                json_match = re.search(r'\{.*\}', content, re.DOTALL)
                if json_match:
                    return json.loads(json_match.group())
                else:
                    raise AIProviderError("Could not parse JSON response from DeepSeek")

        except ImportError:
            raise AIProviderError("pytesseract not installed for OCR processing")
    
    async def _process_openai(self, image_path: Path) -> Dict[str, Any]:
        """Process with OpenAI API."""
        try:
            import openai
        except ImportError:
            raise AIProviderError("OpenAI library not installed")
        
        api_key = settings.get_api_key("openai")
        if not api_key:
            raise AIProviderError("OpenAI API key not configured")
        
        client = openai.AsyncOpenAI(api_key=api_key)
        base64_image = self._encode_image(image_path)
        prompts = get_extraction_prompt("openai")
        
        response = await client.chat.completions.create(
            model="gpt-4-vision-preview",
            messages=[
                {
                    "role": "system",
                    "content": prompts["system"]
                },
                {
                    "role": "user",
                    "content": [
                        {"type": "text", "text": prompts["user"]},
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:image/jpeg;base64,{base64_image}"
                            }
                        }
                    ]
                }
            ],
            max_tokens=4000,
            temperature=0.1
        )
        
        content = response.choices[0].message.content
        try:
            return json.loads(content)
        except json.JSONDecodeError:
            import re
            json_match = re.search(r'\{.*\}', content, re.DOTALL)
            if json_match:
                return json.loads(json_match.group())
            else:
                raise AIProviderError("Could not parse JSON response from OpenAI")
    
    async def _process_anthropic(self, image_path: Path) -> Dict[str, Any]:
        """Process with Anthropic Claude API."""
        try:
            import anthropic
        except ImportError:
            raise AIProviderError("Anthropic library not installed")

        api_key = settings.get_api_key("anthropic")
        if not api_key:
            raise AIProviderError("Anthropic API key not configured")

        client = anthropic.AsyncAnthropic(api_key=api_key)
        base64_image = self._encode_image(image_path)
        prompts = get_extraction_prompt("anthropic")

        response = await client.messages.create(
            model="claude-3-sonnet-20240229",
            max_tokens=4000,
            temperature=0.1,
            system=prompts["system"],
            messages=[
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "image",
                            "source": {
                                "type": "base64",
                                "media_type": "image/jpeg",
                                "data": base64_image
                            }
                        },
                        {"type": "text", "text": prompts["user"]}
                    ]
                }
            ]
        )

        content = response.content[0].text
        try:
            return json.loads(content)
        except json.JSONDecodeError:
            import re
            json_match = re.search(r'\{.*\}', content, re.DOTALL)
            if json_match:
                return json.loads(json_match.group())
            else:
                raise AIProviderError("Could not parse JSON response from Anthropic")

    async def _process_google(self, image_path: Path) -> Dict[str, Any]:
        """Process with Google Gemini API."""
        try:
            import google.generativeai as genai
        except ImportError:
            raise AIProviderError("Google GenerativeAI library not installed")

        api_key = settings.get_api_key("google")
        if not api_key:
            raise AIProviderError("Google API key not configured")

        genai.configure(api_key=api_key)
        model = genai.GenerativeModel('gemini-pro-vision')

        # Load image
        image = Image.open(image_path)
        prompts = get_extraction_prompt("google")

        response = await model.generate_content_async([
            prompts["system"] + "\n\n" + prompts["user"],
            image
        ])

        content = response.text
        try:
            return json.loads(content)
        except json.JSONDecodeError:
            import re
            json_match = re.search(r'\{.*\}', content, re.DOTALL)
            if json_match:
                return json.loads(json_match.group())
            else:
                raise AIProviderError("Could not parse JSON response from Google")

    async def _process_ollama(self, image_path: Path) -> Dict[str, Any]:
        """Process with Ollama local API using OCR + text processing."""
        try:
            import pytesseract
            from PIL import Image

            # Extract text using OCR
            image = Image.open(image_path)
            extracted_text = pytesseract.image_to_string(image)

            if not extracted_text.strip():
                raise AIProviderError("No text could be extracted from the image")

            config = get_provider_config("ollama")
            prompts = get_extraction_prompt("ollama")

            # Modified prompt for text-based processing
            text_prompt = f"""
{prompts["system"]}

Here is the text extracted from an invoice image using OCR:

{extracted_text}

Please extract the invoice data and return it in the specified JSON format.
"""

            payload = {
                "model": settings.ollama_model,
                "prompt": text_prompt,
                "stream": False,
                "options": {
                    "temperature": 0.1,
                    "num_predict": 4000
                }
            }

            async with httpx.AsyncClient(timeout=config.timeout) as client:
                response = await client.post(
                    f"{settings.ollama_base_url}/api/generate",
                    json=payload
                )

                if response.status_code != 200:
                    raise AIProviderError(f"Ollama API error: {response.status_code} - {response.text}")

                result = response.json()
                content = result.get("response", "")

                try:
                    return json.loads(content)
                except json.JSONDecodeError:
                    import re
                    json_match = re.search(r'\{.*\}', content, re.DOTALL)
                    if json_match:
                        return json.loads(json_match.group())
                    else:
                        raise AIProviderError("Could not parse JSON response from Ollama")

        except ImportError:
            raise AIProviderError("pytesseract not installed for OCR processing")

    async def _process_openrouter(self, image_path: Path) -> Dict[str, Any]:
        """Process with OpenRouter API."""
        config = get_provider_config("openrouter")
        api_key = settings.get_api_key("openrouter")

        if not api_key:
            raise AIProviderError("OpenRouter API key not configured")

        base64_image = self._encode_image(image_path)
        prompts = get_extraction_prompt("openrouter")

        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json",
            "HTTP-Referer": "https://invoice-processor.local",
            "X-Title": "Invoice Processor"
        }

        payload = {
            "model": config.model,
            "messages": [
                {
                    "role": "system",
                    "content": prompts["system"]
                },
                {
                    "role": "user",
                    "content": [
                        {"type": "text", "text": prompts["user"]},
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:image/jpeg;base64,{base64_image}"
                            }
                        }
                    ]
                }
            ],
            "max_tokens": config.max_tokens,
            "temperature": config.temperature
        }

        async with httpx.AsyncClient(timeout=config.timeout) as client:
            response = await client.post(
                f"{config.base_url}/chat/completions",
                headers=headers,
                json=payload
            )

            if response.status_code != 200:
                raise AIProviderError(f"OpenRouter API error: {response.status_code} - {response.text}")

            result = response.json()
            content = result["choices"][0]["message"]["content"]

            try:
                return json.loads(content)
            except json.JSONDecodeError:
                import re
                json_match = re.search(r'\{.*\}', content, re.DOTALL)
                if json_match:
                    return json.loads(json_match.group())
                else:
                    raise AIProviderError("Could not parse JSON response from OpenRouter")

    async def _process_mistral(self, image_path: Path) -> Dict[str, Any]:
        """Process with Mistral API."""
        config = get_provider_config("mistral")
        api_key = settings.get_api_key("mistral")

        if not api_key:
            raise AIProviderError("Mistral API key not configured")

        base64_image = self._encode_image(image_path)
        prompts = get_extraction_prompt("mistral")

        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }

        payload = {
            "model": config.model,
            "messages": [
                {
                    "role": "system",
                    "content": prompts["system"]
                },
                {
                    "role": "user",
                    "content": [
                        {"type": "text", "text": prompts["user"]},
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:image/jpeg;base64,{base64_image}"
                            }
                        }
                    ]
                }
            ],
            "max_tokens": config.max_tokens,
            "temperature": config.temperature
        }

        async with httpx.AsyncClient(timeout=config.timeout) as client:
            response = await client.post(
                f"{config.base_url}/chat/completions",
                headers=headers,
                json=payload
            )

            if response.status_code != 200:
                raise AIProviderError(f"Mistral API error: {response.status_code} - {response.text}")

            result = response.json()
            content = result["choices"][0]["message"]["content"]

            try:
                return json.loads(content)
            except json.JSONDecodeError:
                import re
                json_match = re.search(r'\{.*\}', content, re.DOTALL)
                if json_match:
                    return json.loads(json_match.group())
                else:
                    raise AIProviderError("Could not parse JSON response from Mistral")

    async def _process_custom(self, image_path: Path) -> Dict[str, Any]:
        """Process with custom LLM endpoint."""
        if not settings.custom_llm_endpoint:
            raise AIProviderError("Custom LLM endpoint not configured")

        api_key = settings.get_api_key("custom")
        base64_image = self._encode_image(image_path)
        prompts = get_extraction_prompt("custom")

        headers = {
            "Content-Type": "application/json"
        }

        if api_key:
            headers["Authorization"] = f"Bearer {api_key}"

        payload = {
            "model": settings.custom_llm_model,
            "messages": [
                {
                    "role": "system",
                    "content": prompts["system"]
                },
                {
                    "role": "user",
                    "content": [
                        {"type": "text", "text": prompts["user"]},
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:image/jpeg;base64,{base64_image}"
                            }
                        }
                    ]
                }
            ],
            "max_tokens": 4000,
            "temperature": 0.1
        }

        async with httpx.AsyncClient(timeout=60) as client:
            response = await client.post(
                f"{settings.custom_llm_endpoint}/chat/completions",
                headers=headers,
                json=payload
            )

            if response.status_code != 200:
                raise AIProviderError(f"Custom LLM API error: {response.status_code} - {response.text}")

            result = response.json()
            content = result["choices"][0]["message"]["content"]

            try:
                return json.loads(content)
            except json.JSONDecodeError:
                import re
                json_match = re.search(r'\{.*\}', content, re.DOTALL)
                if json_match:
                    return json.loads(json_match.group())
                else:
                    raise AIProviderError("Could not parse JSON response from Custom LLM")

    async def test_provider(self, provider: str) -> Dict[str, Any]:
        """Test if a provider is working correctly."""
        try:
            config = get_provider_config(provider)
            api_key = settings.get_api_key(provider)

            if not config:
                return {"status": "error", "message": f"Unknown provider: {provider}"}

            if provider != "ollama" and not api_key:
                return {"status": "error", "message": f"API key not configured for {provider}"}

            # Test with a simple request (provider-specific implementation needed)
            return {"status": "success", "message": f"{provider} is configured correctly"}

        except Exception as e:
            return {"status": "error", "message": str(e)}
    
    def get_provider_status(self) -> Dict[str, Dict[str, Any]]:
        """Get status of all configured providers."""
        status = {}
        
        for provider in ["openai", "anthropic", "google", "deepseek", "openrouter", "mistral", "ollama", "custom"]:
            config = get_provider_config(provider)
            api_key = settings.get_api_key(provider)
            
            status[provider] = {
                "configured": bool(api_key) if provider != "ollama" else True,
                "available": provider in self.available_providers,
                "model": config.model if config else "Unknown",
                "cost_per_1k": config.cost_per_1k_tokens if config else 0.0
            }
        
        return status
