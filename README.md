# Invoice Processor 🧾

A production-ready Python application for automated invoice processing using multiple AI providers. Extract transaction data from invoice images and automatically organize it into Excel spreadsheets with intelligent categorization and error handling.

## ✨ Features

### 🤖 AI Provider Integration
- **Multiple AI Providers**: DeepSeek, OpenAI GPT-4 Vision, Claude, Gemini, OpenRouter, Mistral
- **Local Processing**: Ollama support for privacy-focused processing
- **Intelligent Fallback**: Automatic provider switching on failures
- **Custom Endpoints**: Support for custom LLM endpoints

### 📊 Data Processing
- **Smart Extraction**: Date, vendor, products, quantities, prices, amounts
- **Multi-language Support**: English, Spanish, French, German, Arabic, Chinese, Japanese
- **Format Support**: JPG, PNG, PDF, HEIC, WebP
- **Data Validation**: Automatic cleaning and standardization

### 📈 Excel Management
- **Monthly Organization**: Separate sheets for each month
- **Yearly Files**: `Invoices_YYYY.xlsx` format
- **Auto-formatting**: Professional styling with conditional formatting
- **Duplicate Prevention**: Smart duplicate detection and handling
- **Summary Reports**: Monthly totals and statistics

### 🛠️ Technical Excellence
- **Clean Architecture**: Modular design with proper separation of concerns
- **Error Handling**: Comprehensive error handling with graceful recovery
- **Logging**: Detailed logging with multiple verbosity levels
- **Progress Tracking**: Real-time progress bars and status updates
- **Type Safety**: Full type hints throughout the codebase

## 🚀 Quick Start

### Prerequisites

- Python 3.8+
- Tesseract OCR (for fallback text extraction)
- At least one AI provider API key

### Installation

1. **Clone the repository**
```bash
git clone <repository-url>
cd invoice-processor
```

2. **Install dependencies**
```bash
pip install -r requirements.txt
```

3. **Configure environment**
```bash
cp .env.template .env
# Edit .env with your API keys
```

4. **Initialize directories**
```bash
python main.py health
```

### Basic Usage

1. **Place invoice images** in the `data/invoices/` folder

2. **Process invoices**
```bash
python main.py process
```

3. **View results** in `data/output/Invoices_YYYY.xlsx`

## 📖 Detailed Usage

### Command Line Interface

#### Process Invoices
```bash
# Process all invoices with default settings
python main.py process

# Process with specific AI provider
python main.py process --provider deepseek

# Process specific folder
python main.py process --input-folder ./custom_invoices

# Custom output file and batch size
python main.py process --output "January_2024.xlsx" --batch-size 3
```

#### Configuration
```bash
# Interactive configuration wizard
python main.py configure

# Test AI provider connections
python main.py test-connections
```

#### Statistics and Monitoring
```bash
# View processing statistics
python main.py stats

# Analyze specific Excel file
python main.py stats --file "Invoices_2024.xlsx"

# System health check
python main.py health
```

### Configuration

#### Environment Variables (.env)
```bash
# Primary AI Provider (recommended: deepseek for cost-effectiveness)
DEFAULT_AI_PROVIDER=deepseek
DEEPSEEK_API_KEY=your_deepseek_key_here

# Fallback Providers
FALLBACK_PROVIDERS=openai,anthropic,google
OPENAI_API_KEY=your_openai_key_here
ANTHROPIC_API_KEY=your_anthropic_key_here
GOOGLE_API_KEY=your_google_key_here

# Processing Settings
MAX_FILE_SIZE_MB=10
BATCH_SIZE=5
CONFIDENCE_THRESHOLD=0.8
LOG_LEVEL=INFO

# File Organization
AUTO_ARCHIVE=true
KEEP_ORIGINALS=true
DUPLICATE_HANDLING=skip
```

#### AI Provider Setup

**DeepSeek (Recommended - Primary)**
- Cost-effective vision model
- Good accuracy for invoice processing
- Sign up at: https://platform.deepseek.com/

**OpenAI GPT-4 Vision**
- Highest accuracy but more expensive
- Get API key: https://platform.openai.com/

**Anthropic Claude**
- Excellent for complex invoices
- Get API key: https://console.anthropic.com/

**Google Gemini**
- Good balance of cost and performance
- Get API key: https://makersuite.google.com/

### Project Structure

```
invoice_processor/
├── main.py                     # CLI application entry point
├── config/
│   ├── settings.py            # Configuration management
│   └── ai_providers.py        # AI provider configurations
├── src/
│   ├── invoice_processor.py   # Core processing logic
│   ├── ai_manager.py          # AI provider management
│   ├── excel_manager.py       # Excel operations
│   ├── utils.py              # Utility functions
│   └── logging_config.py     # Logging and error handling
├── data/
│   ├── invoices/             # Input folder for invoice images
│   ├── processed/            # Processed invoices archive
│   └── output/               # Generated Excel files
├── logs/                     # Application logs
├── requirements.txt          # Dependencies
├── .env                      # Environment variables
└── README.md                # This file
```

## 🔧 Advanced Configuration

### Custom AI Provider

Add custom LLM endpoint support:

```bash
CUSTOM_LLM_ENDPOINT=https://your-api-endpoint.com/v1
CUSTOM_LLM_API_KEY=your_custom_key
CUSTOM_LLM_MODEL=your_model_name
```

### Ollama Local Processing

For privacy-focused local processing:

1. Install Ollama: https://ollama.ai/
2. Pull a vision model: `ollama pull llama3.2-vision`
3. Configure in .env:
```bash
OLLAMA_BASE_URL=http://localhost:11434
OLLAMA_MODEL=llama3.2-vision
```

### Batch Processing Optimization

For high-volume processing:

```bash
# Increase batch size for faster processing
BATCH_SIZE=10

# Enable parallel processing
PARALLEL_PROCESSING=true

# Adjust confidence threshold
CONFIDENCE_THRESHOLD=0.7
```

## 📊 Output Format

### Excel Structure

**Monthly Sheets** (e.g., "January 2024"):
- Date (DD/MM/YYYY format)
- Vendor Name
- Product Details
- Quantity
- Unit Price
- Total Amount
- Currency
- Invoice Number
- Tax Amount
- Payment Method
- Confidence Score
- File Path
- Processed At

**Summary Sheet**:
- Monthly totals and statistics
- Invoice counts per month
- Average amounts
- Overall totals

### Data Validation

The system automatically:
- Standardizes date formats
- Normalizes vendor names
- Validates currency codes
- Calculates confidence scores
- Removes duplicates
- Handles missing data gracefully

## 🐛 Troubleshooting

### Common Issues

**"No AI providers available"**
- Check your API keys in .env file
- Run `python main.py test-connections` to verify

**"File format not supported"**
- Supported formats: JPG, PNG, PDF, HEIC, WebP
- Convert unsupported formats before processing

**"Low confidence results"**
- Try image preprocessing: `--preprocess`
- Use higher-quality scans
- Check if invoice text is clearly readable

**"Memory errors with large files"**
- Reduce batch size: `--batch-size 2`
- Check file sizes (default limit: 10MB)
- Ensure sufficient system memory

### Logging

Logs are stored in the `logs/` directory:
- `invoice_processor.log` - All application logs
- `errors.log` - Error-only logs
- `processing_stats.log` - Processing statistics

View logs in real-time:
```bash
tail -f logs/invoice_processor.log
```

## 🔒 Privacy and Security

### Data Protection
- Local processing option with Ollama
- Temporary files are automatically cleaned
- API keys stored securely in environment variables
- No data retention by AI providers (check provider policies)

### Best Practices
- Use local processing for sensitive documents
- Regularly rotate API keys
- Monitor API usage and costs
- Keep logs secure and rotate regularly

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support and questions:
- Check the troubleshooting section above
- Review logs in the `logs/` directory
- Open an issue on GitHub
- Check AI provider documentation for API-specific issues

## 🎯 Roadmap

- [ ] Web interface for easier usage
- [ ] Integration with accounting software (QuickBooks, Xero)
- [ ] Mobile app for on-the-go processing
- [ ] Advanced analytics and reporting
- [ ] Multi-language UI support
- [ ] Cloud deployment options

---

**Made with ❤️ for efficient invoice processing**
