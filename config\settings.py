"""
Configuration management for the Invoice Processor application.
"""

import os
from pathlib import Path
from typing import List, Optional, Dict, Any
from pydantic import Field, field_validator
from pydantic_settings import BaseSettings
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

class Settings(BaseSettings):
    """Application settings with validation."""
    
    # AI Provider Settings
    openai_api_key: Optional[str] = Field(None, env="OPENAI_API_KEY")
    anthropic_api_key: Optional[str] = Field(None, env="ANTHROPIC_API_KEY")
    google_api_key: Optional[str] = Field(None, env="GOOGLE_API_KEY")
    deepseek_api_key: Optional[str] = Field(None, env="DEEPSEEK_API_KEY")
    openrouter_api_key: Optional[str] = Field(None, env="OPENROUTER_API_KEY")
    mistral_api_key: Optional[str] = Field(None, env="MISTRAL_API_KEY")
    
    # Ollama Settings
    ollama_base_url: str = Field("http://localhost:11434", env="OLLAMA_BASE_URL")
    ollama_model: str = Field("llama3.2-vision", env="OLLAMA_MODEL")
    
    # Custom LLM Settings
    custom_llm_endpoint: Optional[str] = Field(None, env="CUSTOM_LLM_ENDPOINT")
    custom_llm_api_key: Optional[str] = Field(None, env="CUSTOM_LLM_API_KEY")
    custom_llm_model: Optional[str] = Field(None, env="CUSTOM_LLM_MODEL")
    
    # Application Settings
    default_ai_provider: str = Field("deepseek", env="DEFAULT_AI_PROVIDER")
    fallback_providers: str = Field(
        "openai,anthropic,google",
        env="FALLBACK_PROVIDERS"
    )
    max_file_size_mb: int = Field(10, env="MAX_FILE_SIZE_MB")
    supported_languages: str = Field(
        "en,es,fr,de,ar,zh,ja",
        env="SUPPORTED_LANGUAGES"
    )
    log_level: str = Field("INFO", env="LOG_LEVEL")
    max_retries: int = Field(3, env="MAX_RETRIES")
    retry_delay: int = Field(2, env="RETRY_DELAY")
    
    # Processing Settings
    batch_size: int = Field(5, env="BATCH_SIZE")
    parallel_processing: bool = Field(True, env="PARALLEL_PROCESSING")
    confidence_threshold: float = Field(0.8, env="CONFIDENCE_THRESHOLD")
    
    # Output Settings
    output_format: str = Field("xlsx", env="OUTPUT_FORMAT")
    date_format: str = Field("DD/MM/YYYY", env="DATE_FORMAT")
    currency_format: str = Field("local", env="CURRENCY_FORMAT")
    
    # File Organization
    auto_archive: bool = Field(True, env="AUTO_ARCHIVE")
    keep_originals: bool = Field(True, env="KEEP_ORIGINALS")
    duplicate_handling: str = Field("skip", env="DUPLICATE_HANDLING")
    
    # Directory Paths
    base_dir: Path = Field(default_factory=lambda: Path.cwd())
    data_dir: Path = Field(default_factory=lambda: Path.cwd() / "data")
    invoices_dir: Path = Field(default_factory=lambda: Path.cwd() / "data" / "invoices")
    processed_dir: Path = Field(default_factory=lambda: Path.cwd() / "data" / "processed")
    output_dir: Path = Field(default_factory=lambda: Path.cwd() / "data" / "output")
    logs_dir: Path = Field(default_factory=lambda: Path.cwd() / "logs")
    
    @property
    def fallback_providers_list(self) -> List[str]:
        """Get fallback providers as a list."""
        if isinstance(self.fallback_providers, str):
            return [provider.strip() for provider in self.fallback_providers.split(",")]
        return self.fallback_providers

    @property
    def supported_languages_list(self) -> List[str]:
        """Get supported languages as a list."""
        if isinstance(self.supported_languages, str):
            return [lang.strip() for lang in self.supported_languages.split(",")]
        return self.supported_languages
    
    def get_api_key(self, provider: str) -> Optional[str]:
        """Get API key for a specific provider."""
        key_mapping = {
            "openai": self.openai_api_key,
            "anthropic": self.anthropic_api_key,
            "google": self.google_api_key,
            "deepseek": self.deepseek_api_key,
            "openrouter": self.openrouter_api_key,
            "mistral": self.mistral_api_key,
            "custom": self.custom_llm_api_key,
        }
        return key_mapping.get(provider)
    
    def get_available_providers(self) -> List[str]:
        """Get list of providers with valid API keys."""
        available = []
        providers = ["openai", "anthropic", "google", "deepseek", "openrouter", "mistral"]

        for provider in providers:
            if self.get_api_key(provider):
                available.append(provider)

        # Check Ollama availability
        try:
            import httpx
            response = httpx.get(f"{self.ollama_base_url}/api/tags", timeout=5)
            if response.status_code == 200:
                available.append("ollama")
        except:
            pass

        # Check custom LLM
        if self.custom_llm_endpoint and self.custom_llm_api_key:
            available.append("custom")

        return available
    
    def ensure_directories(self):
        """Create necessary directories if they don't exist."""
        directories = [
            self.data_dir,
            self.invoices_dir,
            self.processed_dir,
            self.output_dir,
            self.logs_dir,
        ]
        
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)
    
    class Config:
        env_file = ".env"
        case_sensitive = False

# Global settings instance
settings = Settings()
