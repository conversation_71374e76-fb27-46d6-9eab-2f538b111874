"""
Main Invoice Processing Engine.
"""

import asyncio
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple
import json
from concurrent.futures import ThreadPoolExecutor
from loguru import logger

from config.settings import settings
from src.ai_manager import AIManager, AIProviderError
from src.utils import (
    ImageProcessor, 
    DateProcessor, 
    CurrencyProcessor,
    TextProcessor,
    FileProcessor,
    ValidationUtils
)

class InvoiceProcessingError(Exception):
    """Custom exception for invoice processing errors."""
    pass

class ProcessingResult:
    """Result of invoice processing."""
    
    def __init__(
        self,
        file_path: Path,
        success: bool,
        data: Optional[Dict[str, Any]] = None,
        error: Optional[str] = None,
        processing_time: float = 0.0,
        provider_used: Optional[str] = None
    ):
        self.file_path = file_path
        self.success = success
        self.data = data or {}
        self.error = error
        self.processing_time = processing_time
        self.provider_used = provider_used
        self.timestamp = datetime.now()
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert result to dictionary."""
        return {
            'file_path': str(self.file_path),
            'success': self.success,
            'data': self.data,
            'error': self.error,
            'processing_time': self.processing_time,
            'provider_used': self.provider_used,
            'timestamp': self.timestamp.isoformat()
        }

class InvoiceProcessor:
    """Main invoice processing engine."""
    
    def __init__(self):
        self.ai_manager = AIManager()
        self.image_processor = ImageProcessor()
        self.processed_hashes = set()  # For duplicate detection
        self.processing_stats = {
            'total_processed': 0,
            'successful': 0,
            'failed': 0,
            'duplicates_skipped': 0,
            'total_processing_time': 0.0
        }
        
        # Ensure directories exist
        settings.ensure_directories()
        
        logger.info("Invoice Processor initialized")
    
    async def process_single_invoice(
        self, 
        file_path: Path, 
        provider: Optional[str] = None,
        preprocess: bool = True
    ) -> ProcessingResult:
        """
        Process a single invoice file.
        
        Args:
            file_path: Path to invoice file
            provider: Specific AI provider to use (optional)
            preprocess: Whether to preprocess the image
            
        Returns:
            ProcessingResult object
        """
        start_time = datetime.now()
        
        try:
            logger.info(f"Processing invoice: {file_path}")
            
            # Validate file
            if not file_path.exists():
                raise InvoiceProcessingError(f"File not found: {file_path}")
            
            if not FileProcessor.is_supported_format(file_path):
                raise InvoiceProcessingError(f"Unsupported file format: {file_path.suffix}")
            
            file_size_mb = FileProcessor.get_file_size_mb(file_path)
            if file_size_mb > settings.max_file_size_mb:
                raise InvoiceProcessingError(f"File too large: {file_size_mb:.1f}MB > {settings.max_file_size_mb}MB")
            
            # Check for duplicates
            if settings.duplicate_handling == "skip":
                file_hash = FileProcessor.get_file_hash(file_path)
                if file_hash in self.processed_hashes:
                    logger.warning(f"Duplicate file skipped: {file_path}")
                    self.processing_stats['duplicates_skipped'] += 1
                    return ProcessingResult(
                        file_path=file_path,
                        success=False,
                        error="Duplicate file skipped"
                    )
                self.processed_hashes.add(file_hash)
            
            # Handle PDF files
            if file_path.suffix.lower() == '.pdf':
                image_paths = FileProcessor.convert_pdf_to_images(
                    file_path, 
                    settings.processed_dir / "pdf_pages"
                )
                if not image_paths:
                    raise InvoiceProcessingError("Failed to convert PDF to images")
                
                # Process first page for now (can be extended for multi-page)
                processing_path = image_paths[0]
            else:
                processing_path = file_path
            
            # Preprocess image if requested
            if preprocess and processing_path.suffix.lower() in ['.jpg', '.jpeg', '.png']:
                try:
                    processed_image_path = self.image_processor.enhance_image_pil(
                        processing_path,
                        settings.processed_dir / f"enhanced_{processing_path.name}"
                    )
                    processing_path = processed_image_path
                except Exception as e:
                    logger.warning(f"Image preprocessing failed, using original: {str(e)}")
            
            # Process with AI
            extracted_data = await self.ai_manager.process_invoice(processing_path, provider)
            
            # Validate and clean data
            validated_data = ValidationUtils.validate_invoice_data(extracted_data)
            
            # Calculate confidence score
            confidence = ValidationUtils.calculate_confidence_score(validated_data)
            validated_data['confidence'] = confidence
            
            # Add metadata
            validated_data['file_path'] = str(file_path)
            validated_data['file_size_mb'] = file_size_mb
            validated_data['processed_at'] = datetime.now().isoformat()
            
            processing_time = (datetime.now() - start_time).total_seconds()
            
            # Update stats
            self.processing_stats['total_processed'] += 1
            self.processing_stats['successful'] += 1
            self.processing_stats['total_processing_time'] += processing_time
            
            logger.success(f"Successfully processed {file_path} in {processing_time:.2f}s")
            
            return ProcessingResult(
                file_path=file_path,
                success=True,
                data=validated_data,
                processing_time=processing_time,
                provider_used=extracted_data.get('provider_used')
            )
            
        except Exception as e:
            processing_time = (datetime.now() - start_time).total_seconds()
            error_msg = str(e)
            
            # Update stats
            self.processing_stats['total_processed'] += 1
            self.processing_stats['failed'] += 1
            self.processing_stats['total_processing_time'] += processing_time
            
            logger.error(f"Failed to process {file_path}: {error_msg}")
            
            return ProcessingResult(
                file_path=file_path,
                success=False,
                error=error_msg,
                processing_time=processing_time
            )
    
    async def process_batch(
        self, 
        file_paths: List[Path], 
        provider: Optional[str] = None,
        max_concurrent: int = 3
    ) -> List[ProcessingResult]:
        """
        Process multiple invoice files concurrently.
        
        Args:
            file_paths: List of file paths to process
            provider: Specific AI provider to use (optional)
            max_concurrent: Maximum concurrent processing tasks
            
        Returns:
            List of ProcessingResult objects
        """
        logger.info(f"Starting batch processing of {len(file_paths)} files")
        
        # Create semaphore to limit concurrent processing
        semaphore = asyncio.Semaphore(max_concurrent)
        
        async def process_with_semaphore(file_path: Path) -> ProcessingResult:
            async with semaphore:
                return await self.process_single_invoice(file_path, provider)
        
        # Process all files concurrently
        tasks = [process_with_semaphore(file_path) for file_path in file_paths]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Handle any exceptions
        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.error(f"Exception processing {file_paths[i]}: {str(result)}")
                processed_results.append(ProcessingResult(
                    file_path=file_paths[i],
                    success=False,
                    error=str(result)
                ))
            else:
                processed_results.append(result)
        
        successful = sum(1 for r in processed_results if r.success)
        logger.info(f"Batch processing completed: {successful}/{len(file_paths)} successful")
        
        return processed_results
    
    def discover_invoice_files(self, directory: Path) -> List[Path]:
        """
        Discover invoice files in a directory.
        
        Args:
            directory: Directory to search
            
        Returns:
            List of discovered file paths
        """
        if not directory.exists():
            logger.warning(f"Directory does not exist: {directory}")
            return []
        
        supported_extensions = {'.jpg', '.jpeg', '.png', '.pdf', '.heic', '.webp'}
        discovered_files = []
        
        for ext in supported_extensions:
            pattern = f"*{ext}"
            files = list(directory.glob(pattern))
            files.extend(list(directory.glob(pattern.upper())))
            discovered_files.extend(files)
        
        # Remove duplicates and sort
        discovered_files = sorted(list(set(discovered_files)))
        
        logger.info(f"Discovered {len(discovered_files)} invoice files in {directory}")
        return discovered_files
    
    def archive_processed_file(self, file_path: Path) -> Optional[Path]:
        """
        Archive a processed file.
        
        Args:
            file_path: Path to file to archive
            
        Returns:
            Path to archived file or None if archiving failed
        """
        if not settings.auto_archive:
            return None
        
        try:
            # Create archive directory structure by date
            today = datetime.now()
            archive_dir = settings.processed_dir / str(today.year) / f"{today.month:02d}"
            archive_dir.mkdir(parents=True, exist_ok=True)
            
            # Generate unique filename if file already exists
            archive_path = archive_dir / file_path.name
            counter = 1
            while archive_path.exists():
                stem = file_path.stem
                suffix = file_path.suffix
                archive_path = archive_dir / f"{stem}_{counter}{suffix}"
                counter += 1
            
            # Copy or move file
            if settings.keep_originals:
                import shutil
                shutil.copy2(file_path, archive_path)
                logger.debug(f"Copied file to archive: {archive_path}")
            else:
                file_path.rename(archive_path)
                logger.debug(f"Moved file to archive: {archive_path}")
            
            return archive_path
            
        except Exception as e:
            logger.error(f"Failed to archive file {file_path}: {str(e)}")
            return None
    
    def get_processing_stats(self) -> Dict[str, Any]:
        """Get processing statistics."""
        stats = self.processing_stats.copy()
        
        if stats['total_processed'] > 0:
            stats['success_rate'] = stats['successful'] / stats['total_processed']
            stats['average_processing_time'] = stats['total_processing_time'] / stats['total_processed']
        else:
            stats['success_rate'] = 0.0
            stats['average_processing_time'] = 0.0
        
        return stats
    
    def reset_stats(self):
        """Reset processing statistics."""
        self.processing_stats = {
            'total_processed': 0,
            'successful': 0,
            'failed': 0,
            'duplicates_skipped': 0,
            'total_processing_time': 0.0
        }
        self.processed_hashes.clear()
        logger.info("Processing statistics reset")
    
    async def test_processing_pipeline(self) -> Dict[str, Any]:
        """Test the processing pipeline with available providers."""
        test_results = {}
        
        # Test AI providers
        for provider in self.ai_manager.available_providers:
            try:
                result = await self.ai_manager.test_provider(provider)
                test_results[f"provider_{provider}"] = result
            except Exception as e:
                test_results[f"provider_{provider}"] = {
                    "status": "error",
                    "message": str(e)
                }
        
        # Test image processing
        try:
            # This would need a test image
            test_results["image_processing"] = {"status": "available"}
        except Exception as e:
            test_results["image_processing"] = {"status": "error", "message": str(e)}
        
        # Test file operations
        try:
            settings.ensure_directories()
            test_results["file_operations"] = {"status": "available"}
        except Exception as e:
            test_results["file_operations"] = {"status": "error", "message": str(e)}
        
        return test_results
