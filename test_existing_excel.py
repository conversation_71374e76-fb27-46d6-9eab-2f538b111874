#!/usr/bin/env python3
"""
Test reading existing Excel files and regenerating with currency conversion.
"""

import sys
from pathlib import Path

# Add src to path
sys.path.append(str(Path(__file__).parent / "src"))

from src.excel_manager import ExcelManager
from config.settings import settings

def test_existing_excel_files():
    """Test reading and regenerating existing Excel files with currency conversion."""
    print("Testing Existing Excel Files with Currency Conversion")
    print("=" * 55)
    
    excel_manager = ExcelManager()
    
    # Look for existing Excel files in the output directory
    output_dir = Path("data/output")
    if not output_dir.exists():
        print("❌ Output directory not found. Please run invoice processing first.")
        return
    
    excel_files = list(output_dir.glob("*.xlsx"))
    if not excel_files:
        print("❌ No Excel files found in output directory.")
        return
    
    print(f"Found {len(excel_files)} Excel file(s):")
    for file in excel_files:
        print(f"  📁 {file.name}")
    print()
    
    # Test the first Excel file
    test_file = excel_files[0]
    print(f"Testing file: {test_file.name}")
    print("-" * 40)
    
    try:
        # Get statistics from the file
        stats = excel_manager.get_invoice_statistics(test_file)
        
        print("Current Statistics (may have currency mixing issue):")
        print(f"  Total Sheets: {stats['total_sheets']}")
        print(f"  Total Invoices: {stats['total_invoices']}")
        print(f"  Total Amount: {stats['total_amount']:.2f}")
        print()
        
        print("Monthly Breakdown:")
        for month, data in stats['monthly_breakdown'].items():
            print(f"  {month}: {data['invoices']} invoices, {data['total']:.2f}")
        print()
        
        # Read the invoice data and regenerate with currency conversion
        print("Reading invoice data from Excel...")
        invoice_data = excel_manager.read_invoice_data(test_file)
        
        if invoice_data:
            print(f"✅ Successfully read {len(invoice_data)} invoices")
            
            # Show currency distribution
            currencies = {}
            for invoice in invoice_data:
                currency = invoice.get('currency', 'USD')
                currencies[currency] = currencies.get(currency, 0) + 1
            
            print("\nCurrency Distribution:")
            for currency, count in currencies.items():
                print(f"  {currency}: {count} invoices")
            
            # Regenerate with currency conversion
            output_path = output_dir / f"corrected_{test_file.name}"
            print(f"\nRegenerating with currency conversion...")
            print(f"Base currency: {settings.base_currency}")
            
            corrected_files = excel_manager.save_invoice_data(invoice_data, str(output_path))
            print(f"✅ Corrected file saved: {corrected_files[0]}")
            
            # Get new statistics
            new_stats = excel_manager.get_invoice_statistics(Path(corrected_files[0]))
            
            print("\nCorrected Statistics:")
            print(f"  Total Amount: {new_stats['total_amount']:.2f} {settings.base_currency}")
            print(f"  Difference: {abs(new_stats['total_amount'] - stats['total_amount']):.2f}")
            
        else:
            print("❌ No invoice data found in Excel file")
            
    except Exception as e:
        print(f"❌ Error processing file: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_existing_excel_files()
