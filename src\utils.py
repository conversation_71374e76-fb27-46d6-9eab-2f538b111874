"""
Utility functions for invoice processing.
"""

import re
import hashlib
from datetime import datetime
from typing import List, Optional, Dict, Any, Tuple
from pathlib import Path
import cv2
import numpy as np
from PIL import Image, ImageEnhance, ImageFilter
from dateutil import parser as date_parser
from loguru import logger

class ImageProcessor:
    """Image preprocessing utilities."""
    
    @staticmethod
    def preprocess_image(image_path: Path, output_path: Optional[Path] = None) -> Path:
        """
        Preprocess image for better OCR/AI analysis.
        
        Args:
            image_path: Path to input image
            output_path: Path for processed image (optional)
            
        Returns:
            Path to processed image
        """
        try:
            # Load image
            image = cv2.imread(str(image_path))
            if image is None:
                raise ValueError(f"Could not load image: {image_path}")
            
            # Convert to grayscale
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            
            # Apply denoising
            denoised = cv2.fastNlMeansDenoising(gray)
            
            # Enhance contrast
            clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
            enhanced = clahe.apply(denoised)
            
            # Apply Gaussian blur to reduce noise
            blurred = cv2.GaussianBlur(enhanced, (1, 1), 0)
            
            # Apply threshold to get binary image
            _, thresh = cv2.threshold(blurred, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
            
            # Save processed image
            if output_path is None:
                output_path = image_path.parent / f"processed_{image_path.name}"
            
            cv2.imwrite(str(output_path), thresh)
            logger.debug(f"Preprocessed image saved to: {output_path}")
            
            return output_path
            
        except Exception as e:
            logger.error(f"Error preprocessing image {image_path}: {str(e)}")
            return image_path  # Return original if preprocessing fails
    
    @staticmethod
    def enhance_image_pil(image_path: Path, output_path: Optional[Path] = None) -> Path:
        """
        Enhance image using PIL for better readability.
        
        Args:
            image_path: Path to input image
            output_path: Path for enhanced image (optional)
            
        Returns:
            Path to enhanced image
        """
        try:
            with Image.open(image_path) as img:
                # Convert to RGB if necessary
                if img.mode != 'RGB':
                    img = img.convert('RGB')
                
                # Enhance contrast
                enhancer = ImageEnhance.Contrast(img)
                img = enhancer.enhance(1.2)
                
                # Enhance sharpness
                enhancer = ImageEnhance.Sharpness(img)
                img = enhancer.enhance(1.1)
                
                # Enhance brightness slightly
                enhancer = ImageEnhance.Brightness(img)
                img = enhancer.enhance(1.05)
                
                # Apply unsharp mask
                img = img.filter(ImageFilter.UnsharpMask(radius=1, percent=150, threshold=3))
                
                # Save enhanced image
                if output_path is None:
                    output_path = image_path.parent / f"enhanced_{image_path.name}"
                
                img.save(output_path, quality=95, optimize=True)
                logger.debug(f"Enhanced image saved to: {output_path}")
                
                return output_path
                
        except Exception as e:
            logger.error(f"Error enhancing image {image_path}: {str(e)}")
            return image_path  # Return original if enhancement fails
    
    @staticmethod
    def get_image_info(image_path: Path) -> Dict[str, Any]:
        """Get image information and metadata."""
        try:
            with Image.open(image_path) as img:
                return {
                    "format": img.format,
                    "mode": img.mode,
                    "size": img.size,
                    "width": img.width,
                    "height": img.height,
                    "file_size": image_path.stat().st_size,
                    "has_transparency": img.mode in ('RGBA', 'LA') or 'transparency' in img.info
                }
        except Exception as e:
            logger.error(f"Error getting image info for {image_path}: {str(e)}")
            return {}

class DateProcessor:
    """Date processing and standardization utilities."""
    
    # Common date patterns
    DATE_PATTERNS = [
        r'\d{1,2}[/-]\d{1,2}[/-]\d{2,4}',  # DD/MM/YYYY, MM/DD/YYYY
        r'\d{1,2}\s+\w+\s+\d{2,4}',        # DD Month YYYY
        r'\w+\s+\d{1,2},?\s+\d{2,4}',      # Month DD, YYYY
        r'\d{4}[/-]\d{1,2}[/-]\d{1,2}',    # YYYY/MM/DD
    ]
    
    @staticmethod
    def extract_dates(text: str) -> List[str]:
        """Extract potential dates from text."""
        dates = []
        for pattern in DateProcessor.DATE_PATTERNS:
            matches = re.findall(pattern, text, re.IGNORECASE)
            dates.extend(matches)
        return dates
    
    @staticmethod
    def standardize_date(date_str: str, target_format: str = "%d/%m/%Y") -> Optional[str]:
        """
        Standardize date string to target format with intelligent year correction.

        Args:
            date_str: Input date string
            target_format: Target date format (default: DD/MM/YYYY)

        Returns:
            Standardized date string or None if parsing fails
        """
        if not date_str:
            return None

        try:
            # Try to parse the date
            parsed_date = date_parser.parse(date_str, fuzzy=True)

            # Apply intelligent year correction
            current_year = datetime.now().year
            parsed_year = parsed_date.year

            # If the parsed year is more than 2 years in the future, it's likely wrong
            if parsed_year > current_year + 2:
                # Try to correct common OCR/AI mistakes
                if parsed_year == 2028 and current_year == 2025:
                    # 2028 is likely 2025 (OCR confusion)
                    parsed_date = parsed_date.replace(year=2025)
                elif parsed_year > 2030:
                    # Very future dates are likely OCR errors, use current year
                    parsed_date = parsed_date.replace(year=current_year)

            # If the year is too far in the past (more than 5 years), use current year
            elif parsed_year < current_year - 5:
                parsed_date = parsed_date.replace(year=current_year)

            return parsed_date.strftime(target_format)
        except Exception as e:
            logger.debug(f"Could not parse date '{date_str}': {str(e)}")
            return None
    
    @staticmethod
    def validate_date(date_str: str) -> bool:
        """Validate if a string represents a valid date."""
        try:
            date_parser.parse(date_str, fuzzy=True)
            return True
        except:
            return False

class CurrencyProcessor:
    """Currency detection and processing utilities."""
    
    # Currency symbols and codes
    CURRENCY_SYMBOLS = {
        '$': 'USD',
        '€': 'EUR',
        '£': 'GBP',
        '¥': 'JPY',
        '₹': 'INR',
        '₽': 'RUB',
        '₩': 'KRW',
        '₪': 'ILS',
        '₫': 'VND',
        'د.إ': 'AED',
        'ر.س': 'SAR',
        'ر.ق': 'QAR',
        'د.ك': 'KWD',
        'د.ب': 'BHD',
        'ر.ع': 'OMR',
    }
    
    CURRENCY_CODES = [
        'USD', 'EUR', 'GBP', 'JPY', 'AUD', 'CAD', 'CHF', 'CNY', 'SEK', 'NZD',
        'MXN', 'SGD', 'HKD', 'NOK', 'TRY', 'ZAR', 'BRL', 'INR', 'RUB', 'KRW',
        'AED', 'SAR', 'QAR', 'KWD', 'BHD', 'OMR', 'EGP', 'JOD', 'LBP'
    ]
    
    @staticmethod
    def detect_currency(text: str) -> Optional[str]:
        """Detect currency from text."""
        # Check for currency symbols
        for symbol, code in CurrencyProcessor.CURRENCY_SYMBOLS.items():
            if symbol in text:
                return code
        
        # Check for currency codes
        text_upper = text.upper()
        for code in CurrencyProcessor.CURRENCY_CODES:
            if code in text_upper:
                return code
        
        return None
    
    @staticmethod
    def extract_amounts(text: str) -> List[Tuple[float, str]]:
        """Extract monetary amounts with currency from text."""
        amounts = []
        
        # Pattern for amounts with currency symbols
        symbol_pattern = r'([€£¥₹₽₩₪₫$])\s*(\d+(?:[.,]\d{3})*(?:[.,]\d{2})?)'
        matches = re.findall(symbol_pattern, text)
        
        for symbol, amount_str in matches:
            try:
                # Clean amount string
                amount_str = amount_str.replace(',', '').replace(' ', '')
                amount = float(amount_str)
                currency = CurrencyProcessor.CURRENCY_SYMBOLS.get(symbol, 'USD')
                amounts.append((amount, currency))
            except ValueError:
                continue
        
        # Pattern for amounts with currency codes
        code_pattern = r'(\d+(?:[.,]\d{3})*(?:[.,]\d{2})?)\s*([A-Z]{3})'
        matches = re.findall(code_pattern, text)
        
        for amount_str, currency in matches:
            if currency in CurrencyProcessor.CURRENCY_CODES:
                try:
                    amount_str = amount_str.replace(',', '').replace(' ', '')
                    amount = float(amount_str)
                    amounts.append((amount, currency))
                except ValueError:
                    continue
        
        return amounts
    
    # Exchange rates (approximate, should be updated with real-time rates in production)
    EXCHANGE_RATES = {
        'USD': {'AED': 3.67, 'EUR': 0.85, 'GBP': 0.73, 'JPY': 110.0},
        'AED': {'USD': 0.27, 'EUR': 0.23, 'GBP': 0.20, 'JPY': 30.0},
        'EUR': {'USD': 1.18, 'AED': 4.33, 'GBP': 0.86, 'JPY': 130.0},
        'GBP': {'USD': 1.37, 'AED': 5.03, 'EUR': 1.16, 'JPY': 151.0},
        'JPY': {'USD': 0.009, 'AED': 0.033, 'EUR': 0.0077, 'GBP': 0.0066}
    }

    @staticmethod
    def convert_currency(amount: float, from_currency: str, to_currency: str) -> float:
        """Convert amount from one currency to another."""
        if from_currency == to_currency:
            return amount

        from_currency = from_currency.upper()
        to_currency = to_currency.upper()

        # Check if conversion rate exists
        if from_currency in CurrencyProcessor.EXCHANGE_RATES:
            if to_currency in CurrencyProcessor.EXCHANGE_RATES[from_currency]:
                rate = CurrencyProcessor.EXCHANGE_RATES[from_currency][to_currency]
                return amount * rate

        # If direct conversion not available, try reverse
        if to_currency in CurrencyProcessor.EXCHANGE_RATES:
            if from_currency in CurrencyProcessor.EXCHANGE_RATES[to_currency]:
                rate = CurrencyProcessor.EXCHANGE_RATES[to_currency][from_currency]
                return amount / rate

        # If no conversion available, return original amount with warning
        logger.warning(f"No exchange rate available for {from_currency} to {to_currency}")
        return amount

    @staticmethod
    def format_amount(amount: float, currency: str = 'USD') -> str:
        """Format amount with currency."""
        if currency in ['USD', 'AUD', 'CAD', 'SGD', 'HKD']:
            return f"${amount:,.2f}"
        elif currency == 'EUR':
            return f"€{amount:,.2f}"
        elif currency == 'GBP':
            return f"£{amount:,.2f}"
        elif currency == 'JPY':
            return f"¥{amount:,.0f}"
        else:
            return f"{amount:,.2f} {currency}"

class TextProcessor:
    """Text processing and cleaning utilities."""
    
    @staticmethod
    def clean_text(text: str) -> str:
        """Clean and normalize text."""
        if not text:
            return ""
        
        # Remove extra whitespace
        text = re.sub(r'\s+', ' ', text.strip())
        
        # Remove special characters but keep basic punctuation
        text = re.sub(r'[^\w\s.,;:!?()-]', '', text)
        
        return text
    
    @staticmethod
    def extract_vendor_name(text: str) -> Optional[str]:
        """Extract vendor/store name from text."""
        # Common patterns for store names
        patterns = [
            r'^([A-Z][A-Za-z\s&]+)(?:\n|\r)',  # First line in caps
            r'([A-Z][A-Za-z\s&]{3,30})\s*(?:STORE|SHOP|MARKET|RESTAURANT|CAFE)',
            r'(?:STORE|SHOP|MARKET):\s*([A-Za-z\s&]+)',
        ]
        
        for pattern in patterns:
            match = re.search(pattern, text, re.MULTILINE | re.IGNORECASE)
            if match:
                vendor = match.group(1).strip()
                if len(vendor) > 2:
                    return TextProcessor.clean_text(vendor)
        
        return None
    
    @staticmethod
    def normalize_vendor_name(vendor: str) -> str:
        """Normalize vendor name for consistency."""
        if not vendor:
            return ""
        
        # Common normalizations
        normalizations = {
            r'WAL-?MART.*': 'Walmart',
            r'MCDONALD\'?S.*': 'McDonald\'s',
            r'STARBUCKS.*': 'Starbucks',
            r'TARGET.*': 'Target',
            r'COSTCO.*': 'Costco',
            r'HOME\s*DEPOT.*': 'Home Depot',
            r'BEST\s*BUY.*': 'Best Buy',
        }
        
        vendor_upper = vendor.upper()
        for pattern, normalized in normalizations.items():
            if re.match(pattern, vendor_upper):
                return normalized
        
        # General cleanup
        vendor = re.sub(r'\s+', ' ', vendor.strip())
        vendor = vendor.title()  # Title case
        
        return vendor

class FileProcessor:
    """File processing utilities."""
    
    @staticmethod
    def get_file_hash(file_path: Path) -> str:
        """Get MD5 hash of file for duplicate detection."""
        hash_md5 = hashlib.md5()
        try:
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_md5.update(chunk)
            return hash_md5.hexdigest()
        except Exception as e:
            logger.error(f"Error calculating hash for {file_path}: {str(e)}")
            return ""
    
    @staticmethod
    def is_supported_format(file_path: Path) -> bool:
        """Check if file format is supported."""
        supported_extensions = {'.jpg', '.jpeg', '.png', '.pdf', '.heic', '.webp'}
        return file_path.suffix.lower() in supported_extensions
    
    @staticmethod
    def get_file_size_mb(file_path: Path) -> float:
        """Get file size in MB."""
        try:
            return file_path.stat().st_size / (1024 * 1024)
        except Exception:
            return 0.0
    
    @staticmethod
    def convert_pdf_to_images(pdf_path: Path, output_dir: Path) -> List[Path]:
        """Convert PDF pages to images."""
        try:
            from pdf2image import convert_from_path
            
            images = convert_from_path(pdf_path, dpi=300)
            image_paths = []
            
            for i, image in enumerate(images):
                image_path = output_dir / f"{pdf_path.stem}_page_{i+1}.jpg"
                image.save(image_path, 'JPEG', quality=95)
                image_paths.append(image_path)
            
            logger.info(f"Converted PDF {pdf_path} to {len(image_paths)} images")
            return image_paths
            
        except ImportError:
            logger.error("pdf2image library not installed")
            return []
        except Exception as e:
            logger.error(f"Error converting PDF {pdf_path}: {str(e)}")
            return []

class ValidationUtils:
    """Data validation utilities."""
    
    @staticmethod
    def validate_invoice_data(data: Dict[str, Any]) -> Dict[str, Any]:
        """Validate and clean extracted invoice data."""
        validated = {}

        # Validate date with enhanced correction
        if data.get('date'):
            standardized_date = DateProcessor.standardize_date(data['date'])
            if standardized_date:
                # Additional validation: check if the date makes sense
                try:
                    parsed_date = datetime.strptime(standardized_date, "%d/%m/%Y")
                    current_year = datetime.now().year

                    # If the year is still wrong after standardization, apply more corrections
                    if parsed_date.year > current_year + 1:
                        # Force to current year if still too far in future
                        corrected_date = parsed_date.replace(year=current_year)
                        validated['date'] = corrected_date.strftime("%d/%m/%Y")
                        logger.warning(f"Corrected suspicious date from {standardized_date} to {validated['date']}")
                    else:
                        validated['date'] = standardized_date
                except ValueError:
                    validated['date'] = None
            else:
                validated['date'] = None
        else:
            validated['date'] = None
        
        # Validate vendor name
        vendor = data.get('vendor_name', '').strip()
        if vendor:
            validated['vendor_name'] = TextProcessor.normalize_vendor_name(vendor)
        else:
            validated['vendor_name'] = None
        
        # Validate products
        products = data.get('products', [])
        if isinstance(products, list):
            validated_products = []
            for product in products:
                if isinstance(product, dict):
                    # Safely convert numeric values
                    quantity = product.get('quantity', 1)
                    unit_price = product.get('unit_price', 0.0)
                    total_price = product.get('total_price', 0.0)

                    validated_product = {
                        'description': TextProcessor.clean_text(product.get('description', '')),
                        'quantity': max(1, int(quantity) if quantity is not None else 1),
                        'unit_price': max(0.0, float(unit_price) if unit_price is not None else 0.0),
                        'total_price': max(0.0, float(total_price) if total_price is not None else 0.0)
                    }
                    validated_products.append(validated_product)
            validated['products'] = validated_products
        else:
            validated['products'] = []
        
        # Validate amounts with None checking
        total_amount = data.get('total_amount', 0.0)
        tax_amount = data.get('tax_amount', 0.0)

        validated['total_amount'] = max(0.0, float(total_amount) if total_amount is not None else 0.0)
        validated['tax_amount'] = max(0.0, float(tax_amount) if tax_amount is not None else 0.0)
        
        # Validate currency
        currency = data.get('currency', 'USD')
        if currency and isinstance(currency, str):
            currency = currency.upper()
            if currency in CurrencyProcessor.CURRENCY_CODES:
                validated['currency'] = currency
            else:
                validated['currency'] = 'USD'
        else:
            validated['currency'] = 'USD'
        
        # Other fields
        validated['invoice_number'] = TextProcessor.clean_text(data.get('invoice_number', ''))
        validated['payment_method'] = TextProcessor.clean_text(data.get('payment_method', ''))
        validated['confidence'] = min(1.0, max(0.0, float(data.get('confidence', 0.0))))
        validated['language_detected'] = data.get('language_detected', 'en')
        
        return validated
    
    @staticmethod
    def calculate_confidence_score(data: Dict[str, Any]) -> float:
        """Calculate overall confidence score for extracted data."""
        score = 0.0
        max_score = 0.0
        
        # Date validation (20% weight)
        max_score += 0.2
        if data.get('date') and DateProcessor.validate_date(data['date']):
            score += 0.2
        
        # Vendor name (15% weight)
        max_score += 0.15
        if data.get('vendor_name') and len(data['vendor_name'].strip()) > 2:
            score += 0.15
        
        # Total amount (25% weight)
        max_score += 0.25
        if data.get('total_amount') and data['total_amount'] > 0:
            score += 0.25
        
        # Products (20% weight)
        max_score += 0.2
        products = data.get('products', [])
        if products and len(products) > 0:
            valid_products = sum(1 for p in products if p.get('description'))
            score += 0.2 * (valid_products / len(products))
        
        # Currency (10% weight)
        max_score += 0.1
        if data.get('currency') in CurrencyProcessor.CURRENCY_CODES:
            score += 0.1
        
        # Invoice number (10% weight)
        max_score += 0.1
        if data.get('invoice_number') and len(data['invoice_number'].strip()) > 0:
            score += 0.1
        
        return score / max_score if max_score > 0 else 0.0
