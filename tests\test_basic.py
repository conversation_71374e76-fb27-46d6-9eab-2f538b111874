"""
Basic tests for the Invoice Processor application.
"""

import unittest
import sys
from pathlib import Path

# Add src to path
sys.path.append(str(Path(__file__).parent.parent))

from config.settings import settings
from src.utils import DateProcessor, CurrencyProcessor, TextProcessor, ValidationUtils

class TestBasicFunctionality(unittest.TestCase):
    """Test basic functionality without requiring API keys."""
    
    def test_settings_initialization(self):
        """Test that settings can be initialized."""
        self.assertIsNotNone(settings)
        self.assertIsInstance(settings.default_ai_provider, str)
        self.assertIsInstance(settings.max_file_size_mb, int)
    
    def test_date_processing(self):
        """Test date processing utilities."""
        # Test date standardization
        result = DateProcessor.standardize_date("01/15/2024")
        self.assertIsNotNone(result)
        
        # Test date validation
        self.assertTrue(DateProcessor.validate_date("01/15/2024"))
        self.assertFalse(DateProcessor.validate_date("invalid_date"))
        
        # Test date extraction
        text = "Invoice date: 15/01/2024 Amount: $100"
        dates = DateProcessor.extract_dates(text)
        self.assertGreater(len(dates), 0)
    
    def test_currency_processing(self):
        """Test currency processing utilities."""
        # Test currency detection
        currency = CurrencyProcessor.detect_currency("$100.50")
        self.assertEqual(currency, "USD")
        
        currency = CurrencyProcessor.detect_currency("€50.25")
        self.assertEqual(currency, "EUR")
        
        # Test amount extraction
        amounts = CurrencyProcessor.extract_amounts("Total: $123.45")
        self.assertGreater(len(amounts), 0)
        
        # Test amount formatting
        formatted = CurrencyProcessor.format_amount(123.45, "USD")
        self.assertIn("$", formatted)
        self.assertIn("123.45", formatted)
    
    def test_text_processing(self):
        """Test text processing utilities."""
        # Test text cleaning
        dirty_text = "  Hello   World!  \n\r  "
        clean_text = TextProcessor.clean_text(dirty_text)
        self.assertEqual(clean_text, "Hello World!")
        
        # Test vendor name normalization
        vendor = TextProcessor.normalize_vendor_name("WAL-MART STORE")
        self.assertEqual(vendor, "Walmart")
    
    def test_data_validation(self):
        """Test data validation utilities."""
        # Test invoice data validation
        sample_data = {
            "date": "15/01/2024",
            "vendor_name": "Test Store",
            "total_amount": "100.50",
            "currency": "usd",
            "products": [
                {
                    "description": "Test Product",
                    "quantity": "2",
                    "unit_price": "50.25",
                    "total_price": "100.50"
                }
            ]
        }
        
        validated = ValidationUtils.validate_invoice_data(sample_data)
        
        self.assertIsNotNone(validated["date"])
        self.assertEqual(validated["vendor_name"], "Test Store")
        self.assertEqual(validated["total_amount"], 100.50)
        self.assertEqual(validated["currency"], "USD")
        self.assertEqual(len(validated["products"]), 1)
        
        # Test confidence calculation
        confidence = ValidationUtils.calculate_confidence_score(validated)
        self.assertGreaterEqual(confidence, 0.0)
        self.assertLessEqual(confidence, 1.0)
    
    def test_directory_creation(self):
        """Test that required directories can be created."""
        try:
            settings.ensure_directories()
            
            # Check that directories exist
            self.assertTrue(settings.data_dir.exists())
            self.assertTrue(settings.invoices_dir.exists())
            self.assertTrue(settings.output_dir.exists())
            self.assertTrue(settings.logs_dir.exists())
            
        except Exception as e:
            self.fail(f"Directory creation failed: {str(e)}")

class TestConfigurationValidation(unittest.TestCase):
    """Test configuration validation."""
    
    def test_provider_availability(self):
        """Test provider availability checking."""
        available_providers = settings.get_available_providers()
        self.assertIsInstance(available_providers, list)
        
        # Should at least have some providers configured or available
        # (This test doesn't require API keys)
    
    def test_api_key_retrieval(self):
        """Test API key retrieval (without exposing actual keys)."""
        # Test that the method works (returns None if not configured)
        key = settings.get_api_key("nonexistent_provider")
        self.assertIsNone(key)
        
        # Test with known providers
        for provider in ["openai", "anthropic", "deepseek"]:
            key = settings.get_api_key(provider)
            # Key can be None if not configured, that's fine for testing
            self.assertTrue(key is None or isinstance(key, str))

if __name__ == "__main__":
    # Run tests
    unittest.main(verbosity=2)
