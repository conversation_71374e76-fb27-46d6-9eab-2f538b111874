#!/usr/bin/env python3
"""
Test Excel generation with currency conversion.
"""

import sys
from pathlib import Path
from datetime import datetime

# Add src to path
sys.path.append(str(Path(__file__).parent / "src"))

from src.excel_manager import ExcelManager
from config.settings import settings

def test_excel_with_currency_conversion():
    """Test Excel generation with mixed currencies."""
    print("Testing Excel Generation with Currency Conversion")
    print("=" * 50)
    
    # Sample invoice data with mixed currencies
    sample_data = [
        {
            'date': '2025-06-22',
            'vendor_name': 'Midjourney Inc',
            'total_amount': 10.00,
            'currency': 'USD',
            'invoice_number': '331818F-0023',
            'tax_amount': 0.0,
            'payment_method': 'Digital',
            'confidence': 1.0,
            'file_path': 'test_invoice_1.pdf',
            'processed_at': datetime.now().isoformat(),
            'products': [{'description': 'Basic Plan', 'quantity': 1, 'unit_price': 10.00}]
        },
        {
            'date': '2025-07-09',
            'vendor_name': 'Day To Day Int\'l Hyper',
            'total_amount': 9.99,
            'currency': 'AED',
            'invoice_number': 'R113',
            'tax_amount': 0.0,
            'payment_method': 'MASTER CARD',
            'confidence': 1.0,
            'file_path': 'test_invoice_2.pdf',
            'processed_at': datetime.now().isoformat(),
            'products': [{'description': 'BPIONEER SINGLE PITT', 'quantity': 1, 'unit_price': 9.99}]
        },
        {
            'date': '2025-07-09',
            'vendor_name': 'Day To Day Int\'l Hyper',
            'total_amount': 20.99,
            'currency': 'AED',
            'invoice_number': 'R114',
            'tax_amount': 0.0,
            'payment_method': 'MASTER CARD',
            'confidence': 1.0,
            'file_path': 'test_invoice_3.pdf',
            'processed_at': datetime.now().isoformat(),
            'products': [{'description': 'MENS T-SHIRT DT', 'quantity': 1, 'unit_price': 20.99}]
        }
    ]
    
    # Create Excel manager
    excel_manager = ExcelManager()
    
    # Generate Excel file
    output_path = Path("test_currency_conversion_output.xlsx")
    
    try:
        excel_files = excel_manager.save_invoice_data(sample_data, str(output_path))
        
        print(f"✅ Excel file generated successfully!")
        print(f"📁 Output file: {excel_files[0]}")
        print()
        print("Key improvements:")
        print("- June USD amount converted to AED in summary")
        print("- July AED amounts remain in AED")
        print("- Summary shows totals in base currency (AED)")
        print("- Column headers indicate currency")
        print()
        print(f"Base currency setting: {settings.base_currency}")
        
        # Show conversion details
        print("\nConversion details:")
        usd_amount = 10.00
        aed_equivalent = 36.70  # 10.00 * 3.67
        print(f"June: ${usd_amount} USD → {aed_equivalent} AED")
        print(f"July: 30.98 AED → 30.98 AED (no conversion needed)")
        print(f"Total: {aed_equivalent + 30.98:.2f} AED")
        
    except Exception as e:
        print(f"❌ Error generating Excel: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_excel_with_currency_conversion()
