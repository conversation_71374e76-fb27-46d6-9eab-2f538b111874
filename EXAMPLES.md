# Usage Examples 📚

This document provides practical examples of using the Invoice Processor application in various scenarios.

## Basic Usage Examples

### Example 1: Process Single Invoice
```bash
# Place invoice.jpg in data/invoices/
python main.py process

# Output: data/output/Invoices_2024.xlsx
```

### Example 2: Process with Specific Provider
```bash
# Use OpenAI for high accuracy
python main.py process --provider openai

# Use DeepSeek for cost efficiency
python main.py process --provider deepseek
```

### Example 3: Custom Input Folder
```bash
# Process invoices from custom folder
python main.py process --input-folder "C:\Users\<USER>\Documents\Receipts"

# Process with custom output filename
python main.py process --output "January_Expenses.xlsx"
```

## Advanced Processing Examples

### Example 4: Batch Processing with Custom Settings
```bash
# Process 10 files concurrently with preprocessing disabled
python main.py process --batch-size 10 --no-preprocess

# Process with specific provider and custom output
python main.py process --provider anthropic --output "Q1_2024.xlsx" --batch-size 3
```

### Example 5: High-Volume Processing
```bash
# For processing 100+ invoices efficiently
python main.py process --batch-size 15 --provider deepseek
```

## Configuration Examples

### Example 6: Interactive Setup
```bash
# Run configuration wizard
python main.py configure

# Test all configured providers
python main.py test-connections
```

### Example 7: Environment Configuration

**.env for Small Business (Cost-Optimized)**
```bash
DEFAULT_AI_PROVIDER=deepseek
FALLBACK_PROVIDERS=google,openai
DEEPSEEK_API_KEY=sk-xxx
GOOGLE_API_KEY=AIza-xxx
BATCH_SIZE=5
CONFIDENCE_THRESHOLD=0.75
AUTO_ARCHIVE=true
KEEP_ORIGINALS=true
```

**.env for Enterprise (Accuracy-Focused)**
```bash
DEFAULT_AI_PROVIDER=openai
FALLBACK_PROVIDERS=anthropic,deepseek
OPENAI_API_KEY=sk-xxx
ANTHROPIC_API_KEY=sk-ant-xxx
DEEPSEEK_API_KEY=sk-xxx
BATCH_SIZE=10
CONFIDENCE_THRESHOLD=0.85
MAX_RETRIES=5
```

**.env for Privacy-Focused (Local Processing)**
```bash
DEFAULT_AI_PROVIDER=ollama
OLLAMA_BASE_URL=http://localhost:11434
OLLAMA_MODEL=llama3.2-vision
BATCH_SIZE=3
KEEP_ORIGINALS=true
AUTO_ARCHIVE=true
```

## Monitoring and Statistics Examples

### Example 8: View Processing Statistics
```bash
# View current session statistics
python main.py stats

# Analyze specific Excel file
python main.py stats --file "data/output/Invoices_2024.xlsx"

# Check system health
python main.py health
```

## Real-World Scenarios

### Scenario 1: Monthly Expense Processing

**Setup:**
```bash
# Create monthly folder structure
mkdir -p "invoices/2024/01-January"
mkdir -p "invoices/2024/02-February"
```

**Processing:**
```bash
# Process January invoices
python main.py process --input-folder "invoices/2024/01-January" --output "January_2024.xlsx"

# Process February invoices
python main.py process --input-folder "invoices/2024/02-February" --output "February_2024.xlsx"
```

### Scenario 2: Multi-Language Invoice Processing

**Configuration (.env):**
```bash
SUPPORTED_LANGUAGES=en,es,fr,de,ar,zh,ja
DEFAULT_AI_PROVIDER=openai  # Better for multi-language
CONFIDENCE_THRESHOLD=0.7    # Lower threshold for non-English
```

**Processing:**
```bash
python main.py process --input-folder "international_invoices"
```

### Scenario 3: High-Accuracy Processing for Audit

**Configuration (.env):**
```bash
DEFAULT_AI_PROVIDER=openai
FALLBACK_PROVIDERS=anthropic,google
CONFIDENCE_THRESHOLD=0.9
MAX_RETRIES=5
RETRY_DELAY=3
```

**Processing:**
```bash
python main.py process --provider openai --batch-size 2
```

## Sample Output Examples

### Example Excel Structure

**Sheet: "January 2024"**
| Date | Vendor Name | Product Details | Quantity | Unit Price | Total Amount | Currency | Invoice Number | Tax Amount | Payment Method | Confidence |
|------|-------------|-----------------|----------|------------|--------------|----------|----------------|------------|----------------|------------|
| 15/01/2024 | Walmart | Groceries - Mixed items | 1 | 45.67 | 45.67 | USD | ********** | 3.45 | Card | 95% |
| 16/01/2024 | Shell Gas Station | Gasoline | 12.5 | 3.45 | 43.13 | USD | SH-789456 | 2.89 | Card | 92% |

**Summary Sheet:**
| Month | Invoice Count | Total Amount | Average Amount |
|-------|---------------|--------------|----------------|
| January 2024 | 25 | $1,234.56 | $49.38 |
| February 2024 | 30 | $1,567.89 | $52.26 |

### Example Log Output

```
2024-01-15 10:30:15 | INFO | Processing invoice: receipt_001.jpg
2024-01-15 10:30:16 | INFO | Attempting to process with provider: deepseek
2024-01-15 10:30:18 | SUCCESS | Successfully processed receipt_001.jpg in 2.34s
2024-01-15 10:30:18 | INFO | Confidence score: 0.92
2024-01-15 10:30:18 | INFO | Extracted: Walmart, $45.67, 15/01/2024
```

## Error Handling Examples

### Example 9: Handling Failed Processing
```bash
# Process with verbose logging
LOG_LEVEL=DEBUG python main.py process

# Check error logs
tail -f logs/errors.log
```

### Example 10: Recovery from Provider Failures
```bash
# If primary provider fails, fallback providers are used automatically
# Check which provider was used in the output
python main.py stats
```

## Integration Examples

### Example 11: Automated Processing Script

**process_monthly.py:**
```python
#!/usr/bin/env python3
import subprocess
import sys
from datetime import datetime
from pathlib import Path

def process_monthly_invoices():
    """Process invoices for current month."""
    current_month = datetime.now().strftime("%B_%Y")
    input_folder = f"invoices/{current_month}"
    output_file = f"{current_month}_Invoices.xlsx"

    if not Path(input_folder).exists():
        print(f"No invoices found for {current_month}")
        return

    # Run processing
    cmd = [
        sys.executable, "main.py", "process",
        "--input-folder", input_folder,
        "--output", output_file,
        "--batch-size", "10"
    ]

    result = subprocess.run(cmd, capture_output=True, text=True)

    if result.returncode == 0:
        print(f"Successfully processed {current_month} invoices")
    else:
        print(f"Error processing invoices: {result.stderr}")

if __name__ == "__main__":
    process_monthly_invoices()
```

### Example 12: Batch Processing Script

**batch_process.py:**
```python
#!/usr/bin/env python3
import subprocess
import sys
from pathlib import Path

def batch_process_folders():
    """Process all folders in invoices directory."""
    invoices_dir = Path("invoices")

    for folder in invoices_dir.iterdir():
        if folder.is_dir():
            print(f"Processing folder: {folder.name}")

            cmd = [
                sys.executable, "main.py", "process",
                "--input-folder", str(folder),
                "--output", f"{folder.name}_Invoices.xlsx"
            ]

            subprocess.run(cmd)

if __name__ == "__main__":
    batch_process_folders()
```

## Performance Optimization Examples

### Example 13: Memory-Optimized Processing
```bash
# For systems with limited memory
python main.py process --batch-size 2 --no-preprocess
```

### Example 14: Speed-Optimized Processing
```bash
# For fast processing of clear images
python main.py process --batch-size 20 --provider deepseek --confidence-threshold 0.7
```

## Troubleshooting Examples

### Example 15: Debug Low Confidence Results
```bash
# Enable debug logging
LOG_LEVEL=DEBUG python main.py process --provider openai

# Check processing logs
grep "confidence" logs/invoice_processor.log
```

### Example 16: Handle Large Files
```bash
# Increase file size limit
MAX_FILE_SIZE_MB=20 python main.py process

# Or resize images before processing
```

## Custom Workflow Examples

### Example 17: Weekly Processing Workflow
```bash
#!/bin/bash
# weekly_process.sh

echo "Starting weekly invoice processing..."

# Process new invoices
python main.py process --input-folder "weekly_invoices"

# Generate statistics
python main.py stats

# Archive processed files
mv weekly_invoices/* processed_invoices/

echo "Weekly processing complete!"
```

### Example 18: Quality Control Workflow
```bash
# Process with high confidence threshold
python main.py process --confidence-threshold 0.9

# Review low-confidence results
grep "confidence.*0\.[0-8]" logs/invoice_processor.log

# Reprocess failed items with different provider
python main.py process --provider anthropic --input-folder "failed_invoices"
```

---

These examples cover common use cases and scenarios. Adapt them to your specific needs and workflow requirements.