"""
Logging configuration and error handling utilities.
"""

import sys
import traceback
from datetime import datetime
from pathlib import Path
from typing import Optional, Dict, Any, Callable
from functools import wraps
import json

from loguru import logger
from config.settings import settings

class LoggingManager:
    """Manages application logging configuration."""
    
    def __init__(self):
        self.logs_dir = settings.logs_dir
        self.logs_dir.mkdir(parents=True, exist_ok=True)
        self.setup_logging()
    
    def setup_logging(self):
        """Configure loguru logging."""
        # Remove default handler
        logger.remove()
        
        # Console handler with colors
        logger.add(
            sys.stdout,
            format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
            level=settings.log_level,
            colorize=True,
            backtrace=True,
            diagnose=True
        )
        
        # File handler for all logs
        logger.add(
            self.logs_dir / "invoice_processor.log",
            format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
            level="DEBUG",
            rotation="10 MB",
            retention="30 days",
            compression="zip",
            backtrace=True,
            diagnose=True
        )
        
        # Error-only file handler
        logger.add(
            self.logs_dir / "errors.log",
            format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
            level="ERROR",
            rotation="5 MB",
            retention="60 days",
            compression="zip",
            backtrace=True,
            diagnose=True
        )
        
        # Processing statistics log
        logger.add(
            self.logs_dir / "processing_stats.log",
            format="{time:YYYY-MM-DD HH:mm:ss} | {message}",
            level="INFO",
            filter=lambda record: "STATS" in record["message"],
            rotation="1 day",
            retention="90 days"
        )
        
        logger.info("Logging system initialized")
    
    def log_processing_stats(self, stats: Dict[str, Any]):
        """Log processing statistics."""
        stats_message = f"STATS | {json.dumps(stats, indent=2)}"
        logger.info(stats_message)
    
    def log_error_with_context(
        self, 
        error: Exception, 
        context: Dict[str, Any], 
        operation: str = "Unknown"
    ):
        """Log error with additional context."""
        error_info = {
            "operation": operation,
            "error_type": type(error).__name__,
            "error_message": str(error),
            "context": context,
            "timestamp": datetime.now().isoformat(),
            "traceback": traceback.format_exc()
        }
        
        logger.error(f"Error in {operation}: {json.dumps(error_info, indent=2)}")

class ErrorHandler:
    """Centralized error handling utilities."""
    
    @staticmethod
    def handle_file_error(file_path: Path, operation: str, error: Exception) -> Dict[str, Any]:
        """Handle file-related errors."""
        error_info = {
            "type": "file_error",
            "file_path": str(file_path),
            "operation": operation,
            "error": str(error),
            "exists": file_path.exists() if file_path else False,
            "is_file": file_path.is_file() if file_path and file_path.exists() else False,
            "size": file_path.stat().st_size if file_path and file_path.exists() and file_path.is_file() else 0
        }
        
        logger.error(f"File error during {operation}: {error_info}")
        return error_info
    
    @staticmethod
    def handle_ai_provider_error(provider: str, error: Exception, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """Handle AI provider-related errors."""
        error_info = {
            "type": "ai_provider_error",
            "provider": provider,
            "error": str(error),
            "error_type": type(error).__name__,
            "context": context or {}
        }
        
        logger.error(f"AI provider error with {provider}: {error_info}")
        return error_info
    
    @staticmethod
    def handle_processing_error(
        file_path: Path, 
        stage: str, 
        error: Exception, 
        context: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Handle processing-related errors."""
        error_info = {
            "type": "processing_error",
            "file_path": str(file_path),
            "stage": stage,
            "error": str(error),
            "error_type": type(error).__name__,
            "context": context or {}
        }
        
        logger.error(f"Processing error at {stage} for {file_path}: {error_info}")
        return error_info
    
    @staticmethod
    def handle_excel_error(operation: str, error: Exception, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """Handle Excel-related errors."""
        error_info = {
            "type": "excel_error",
            "operation": operation,
            "error": str(error),
            "error_type": type(error).__name__,
            "context": context or {}
        }
        
        logger.error(f"Excel error during {operation}: {error_info}")
        return error_info

def retry_on_failure(max_retries: int = 3, delay: float = 1.0, exceptions: tuple = (Exception,)):
    """Decorator to retry function on failure."""
    def decorator(func: Callable):
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            last_exception = None
            
            for attempt in range(max_retries + 1):
                try:
                    return await func(*args, **kwargs)
                except exceptions as e:
                    last_exception = e
                    if attempt < max_retries:
                        logger.warning(f"Attempt {attempt + 1} failed for {func.__name__}: {str(e)}. Retrying in {delay}s...")
                        import asyncio
                        await asyncio.sleep(delay)
                    else:
                        logger.error(f"All {max_retries + 1} attempts failed for {func.__name__}")
            
            raise last_exception
        
        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            last_exception = None
            
            for attempt in range(max_retries + 1):
                try:
                    return func(*args, **kwargs)
                except exceptions as e:
                    last_exception = e
                    if attempt < max_retries:
                        logger.warning(f"Attempt {attempt + 1} failed for {func.__name__}: {str(e)}. Retrying in {delay}s...")
                        import time
                        time.sleep(delay)
                    else:
                        logger.error(f"All {max_retries + 1} attempts failed for {func.__name__}")
            
            raise last_exception
        
        # Return appropriate wrapper based on function type
        import asyncio
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
    
    return decorator

def log_execution_time(operation_name: str = None):
    """Decorator to log function execution time."""
    def decorator(func: Callable):
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            start_time = datetime.now()
            operation = operation_name or func.__name__
            
            try:
                result = await func(*args, **kwargs)
                execution_time = (datetime.now() - start_time).total_seconds()
                logger.info(f"Operation '{operation}' completed in {execution_time:.2f}s")
                return result
            except Exception as e:
                execution_time = (datetime.now() - start_time).total_seconds()
                logger.error(f"Operation '{operation}' failed after {execution_time:.2f}s: {str(e)}")
                raise
        
        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            start_time = datetime.now()
            operation = operation_name or func.__name__
            
            try:
                result = func(*args, **kwargs)
                execution_time = (datetime.now() - start_time).total_seconds()
                logger.info(f"Operation '{operation}' completed in {execution_time:.2f}s")
                return result
            except Exception as e:
                execution_time = (datetime.now() - start_time).total_seconds()
                logger.error(f"Operation '{operation}' failed after {execution_time:.2f}s: {str(e)}")
                raise
        
        # Return appropriate wrapper based on function type
        import asyncio
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
    
    return decorator

def safe_execute(func: Callable, *args, default_return=None, log_errors: bool = True, **kwargs):
    """Safely execute a function with error handling."""
    try:
        return func(*args, **kwargs)
    except Exception as e:
        if log_errors:
            logger.error(f"Error executing {func.__name__}: {str(e)}")
        return default_return

class ProgressTracker:
    """Track and log progress for long-running operations."""
    
    def __init__(self, total_items: int, operation_name: str = "Processing"):
        self.total_items = total_items
        self.operation_name = operation_name
        self.completed_items = 0
        self.failed_items = 0
        self.start_time = datetime.now()
        self.last_log_time = self.start_time
        
        logger.info(f"Starting {operation_name}: {total_items} items to process")
    
    def update(self, success: bool = True, item_name: str = None):
        """Update progress."""
        if success:
            self.completed_items += 1
        else:
            self.failed_items += 1
        
        current_time = datetime.now()
        
        # Log progress every 10 items or every 30 seconds
        if (self.completed_items + self.failed_items) % 10 == 0 or \
           (current_time - self.last_log_time).total_seconds() > 30:
            self._log_progress(item_name)
            self.last_log_time = current_time
    
    def _log_progress(self, item_name: str = None):
        """Log current progress."""
        total_processed = self.completed_items + self.failed_items
        progress_percent = (total_processed / self.total_items) * 100
        
        elapsed_time = (datetime.now() - self.start_time).total_seconds()
        items_per_second = total_processed / elapsed_time if elapsed_time > 0 else 0
        
        eta_seconds = (self.total_items - total_processed) / items_per_second if items_per_second > 0 else 0
        eta_minutes = eta_seconds / 60
        
        progress_msg = (
            f"{self.operation_name} Progress: {total_processed}/{self.total_items} "
            f"({progress_percent:.1f}%) | Success: {self.completed_items} | "
            f"Failed: {self.failed_items} | Speed: {items_per_second:.1f} items/s | "
            f"ETA: {eta_minutes:.1f} minutes"
        )
        
        if item_name:
            progress_msg += f" | Current: {item_name}"
        
        logger.info(progress_msg)
    
    def finish(self):
        """Log final results."""
        total_time = (datetime.now() - self.start_time).total_seconds()
        success_rate = (self.completed_items / self.total_items) * 100 if self.total_items > 0 else 0
        
        final_msg = (
            f"{self.operation_name} Completed: {self.completed_items}/{self.total_items} successful "
            f"({success_rate:.1f}%) | {self.failed_items} failed | "
            f"Total time: {total_time:.1f}s"
        )
        
        logger.success(final_msg)

class HealthChecker:
    """System health checking utilities."""
    
    @staticmethod
    def check_disk_space(path: Path, min_free_gb: float = 1.0) -> Dict[str, Any]:
        """Check available disk space."""
        try:
            import shutil
            total, used, free = shutil.disk_usage(path)
            
            free_gb = free / (1024**3)
            total_gb = total / (1024**3)
            used_gb = used / (1024**3)
            
            status = {
                "path": str(path),
                "total_gb": round(total_gb, 2),
                "used_gb": round(used_gb, 2),
                "free_gb": round(free_gb, 2),
                "usage_percent": round((used_gb / total_gb) * 100, 1),
                "sufficient_space": free_gb >= min_free_gb
            }
            
            if not status["sufficient_space"]:
                logger.warning(f"Low disk space: {free_gb:.1f}GB free (minimum: {min_free_gb}GB)")
            
            return status
            
        except Exception as e:
            logger.error(f"Error checking disk space: {str(e)}")
            return {"error": str(e)}
    
    @staticmethod
    def check_memory_usage() -> Dict[str, Any]:
        """Check memory usage."""
        try:
            import psutil
            memory = psutil.virtual_memory()
            
            status = {
                "total_gb": round(memory.total / (1024**3), 2),
                "available_gb": round(memory.available / (1024**3), 2),
                "used_gb": round(memory.used / (1024**3), 2),
                "usage_percent": memory.percent,
                "sufficient_memory": memory.percent < 90
            }
            
            if not status["sufficient_memory"]:
                logger.warning(f"High memory usage: {memory.percent:.1f}%")
            
            return status
            
        except ImportError:
            return {"error": "psutil not installed"}
        except Exception as e:
            logger.error(f"Error checking memory usage: {str(e)}")
            return {"error": str(e)}

# Initialize logging manager
logging_manager = LoggingManager()
