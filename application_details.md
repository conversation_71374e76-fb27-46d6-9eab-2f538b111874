Project Overview
Create a Python application that processes invoice images using multiple AI providers to extract transaction data and automatically organize it into Excel spreadsheets with intelligent categorization and error handling.

Core Requirements

1. AI Provider Integration
- OpenAI: GPT-4 Vision API for invoice analysis
- Anthropic Claude: Vision capabilities for invoice processing
- Google Gemini: Vision API integration
- Deepseek: API integration for invoice analysis
- OpenRouter: Multi-model proxy support
- Mistral: Vision-capable models
- Ollama: Local LLM support (llama-vision, etc.) Models directory G:\ollamamodels
- Custom LLM: Configurable endpoint support

2. File Structure
```
invoice_processor/
├── main.py                  Main application entry point
├── config/
│   ├── __init__.py
│   ├── settings.py         Configuration management
│   └── ai_providers.py     AI provider configurations
├── src/
│   ├── __init__.py
│   ├── invoice_processor.py   Core processing logic
│   ├── ai_manager.py         AI provider management
│   ├── excel_manager.py      Excel operations
│   ├── image_processor.py    Image preprocessing
│   └── utils.py             Utility functions
├── data/
│   ├── invoices/            Input folder for invoice images
│   ├── processed/           Processed invoices archive
│   └── output/              Generated Excel files
├── logs/                    Application logs
├── requirements.txt         Dependencies
├── .env                     Environment variables
└── README.md               Documentation
```

3. Data Processing Requirements

Input Processing
- Supported formats: JPG, PNG, PDF, HEIC, WebP
- Multi-language support: English, Spanish, French, German, Arabic, Chinese, Japanese, etc.
- Date format handling: DD/MM/YY, DD-MMM-YYYY, MM/DD/YYYY, YYYY-MM-DD, and regional variants
- Currency detection: AED, USD, EUR, GBP, etc.

Output Schema
Excel columns:
- Date: Standardized to DD/MM/YYYY
- Vendor Name: Extracted business/store name
- Product Details: Item descriptions (concatenated if multiple)
- Quantity: Number of items
- Unit Price: Price per item
- Total Amount: Final amount paid
- Currency: Detected currency
- Invoice Number: If available
- Tax Amount: Extracted tax information
- Payment Method: Cash/Card/Digital (if detectable)

4. Technical Implementation

Core Dependencies
```python
 requirements.txt
openai>=1.0.0
anthropic>=0.8.0
google-generativeai>=0.3.0
requests>=2.31.0
pandas>=2.0.0
openpyxl>=3.1.0
Pillow>=10.0.0
python-dotenv>=1.0.0
PyPDF2>=3.0.0
pytesseract>=0.3.10
opencv-python>=4.8.0
pydantic>=2.0.0
typer>=0.9.0
rich>=13.0.0
loguru>=0.7.0
```

Environment Configuration (.env)
```bash
 AI Provider API Keys
OPENAI_API_KEY=your_openai_key
ANTHROPIC_API_KEY=your_anthropic_key
GOOGLE_API_KEY=your_google_key
DEEPSEEK_API_KEY=your_deepseek_key
OPENROUTER_API_KEY=your_openrouter_key
MISTRAL_API_KEY=your_mistral_key

 Ollama Configuration
OLLAMA_BASE_URL=http://localhost:11434
OLLAMA_MODEL=llama3.2-vision

 Custom LLM Configuration
CUSTOM_LLM_ENDPOINT=
CUSTOM_LLM_API_KEY=
CUSTOM_LLM_MODEL=

 Application Settings
DEFAULT_AI_PROVIDER=openai
MAX_FILE_SIZE_MB=10
SUPPORTED_LANGUAGES=en,es,fr,de,ar,zh,ja
LOG_LEVEL=INFO
```

5. Key Features to Implement

AI Processing Pipeline
1. Image Preprocessing
   - Automatic rotation correction
   - Contrast and brightness adjustment
   - Noise reduction for better OCR
   - Format conversion (PDF to images)

2. Intelligent Invoice Analysis
   - Multi-pass AI analysis for accuracy
   - Cross-validation between providers
   - Confidence scoring for extracted data
   - Fallback OCR for failed AI extraction

3. Data Validation & Cleaning
   - Date format standardization
   - Currency conversion options
   - Duplicate detection
   - Data completeness checks

Excel Management
- Monthly organization: Separate sheets for each month
- Yearly files: `Invoices[YYYY].xlsx` format
- Auto-formatting: Headers, currency formatting, date formatting
- Summary sheets: Monthly totals and statistics
- Data validation: Prevent duplicate entries

Error Handling & Logging
- Comprehensive logging: All operations, errors, and processing times
- Graceful failures: Continue processing other files if one fails
- Retry mechanisms: For API failures and temporary issues
- User-friendly error messages: Clear feedback on what went wrong

6. User Interface Requirements

Command Line Interface
```bash
 Process all invoices with default settings
python main.py process

 Process with specific AI provider
python main.py process --provider openai

 Process specific folder
python main.py process --input-folder ./custom_invoices

 View processing statistics
python main.py stats

 Configure AI providers
python main.py configure

 Test AI provider connections
python main.py test-connections
```

Configuration Management
- Interactive setup wizard for first-time users
- Provider-specific configuration validation
- API key testing and validation
- Model selection for each provider

7. Advanced Features

Multi-Language Support
- Automatic language detection
- Language-specific processing prompts
- Regional date/currency format handling
- Unicode support for all text extraction

Data Intelligence
- Vendor name standardization (e.g., "Walmart", "WAL-MART", "Walmart Store" → "Walmart")
- Product categorization suggestions
- Spending pattern analysis
- Monthly/yearly summary reports

Integration Capabilities
- Export to different formats (CSV, JSON, PDF reports)
- Integration with accounting software APIs
- Email notifications for processing completion
- Backup and sync capabilities

8. Error Handling Strategy

File Processing Errors
- Invalid image formats
- Corrupted files
- Large file handling
- Permission errors

AI Provider Errors
- API rate limiting
- Authentication failures
- Model unavailability
- Response parsing errors

Data Processing Errors
- Invalid date formats
- Currency conversion issues
- Missing required fields
- Excel file conflicts

9. Performance Optimization

Processing Efficiency
- Batch processing for multiple invoices
- Parallel processing where possible
- Caching for repeated operations
- Progress tracking for large batches

Resource Management
- Memory optimization for large images
- Temporary file cleanup
- Connection pooling for API calls
- Configurable timeout settings

10. Security Considerations

Data Protection
- Local processing to protect sensitive data
- Secure API key storage
- Temporary file encryption
- Audit logging for data access

Privacy Features
- Option to redact sensitive information
- Local-only processing mode
- Data retention policies
- Secure deletion of processed files