#!/usr/bin/env python3
"""
Test script using your actual invoice data to demonstrate the currency conversion fix.
"""

import sys
from pathlib import Path

# Add src to path
sys.path.append(str(Path(__file__).parent / "src"))

from src.utils import CurrencyProcessor
from config.settings import settings

def test_your_actual_data():
    """Test with your actual invoice data."""
    print("INVOICE SUMMARY CORRECTION TEST")
    print("=" * 50)
    
    # Your actual data from the screenshots
    june_invoices = [
        {"vendor": "Midjourney Inc", "amount": 10.00, "currency": "USD"}
    ]
    
    july_invoices = [
        {"vendor": "Day To Day Int'l Hyper", "amount": 9.99, "currency": "AED"},
        {"vendor": "Day To Day Int'l Hyper", "amount": 20.99, "currency": "AED"},
        {"vendor": "Day To Day Int'l Hyper", "amount": 20.99, "currency": "AED"},
        {"vendor": "Day To Day Int'l Hyper", "amount": 20.99, "currency": "AED"},
        {"vendor": "Day To Day Int'l Hyper", "amount": 10.99, "currency": "AED"},
        {"vendor": "Day To Day Int'l Hyper", "amount": 20.99, "currency": "AED"},
        {"vendor": "Day To Day Int'l Hyper", "amount": 20.99, "currency": "AED"},
        {"vendor": "Day To Day Int'l Hyper", "amount": 9.99, "currency": "AED"},
        {"vendor": "Day To Day Int'l Hyper", "amount": 20.99, "currency": "AED"},
        {"vendor": "Day To Day Int'l Hyper", "amount": 20.99, "currency": "AED"},
        {"vendor": "Day To Day Int'l Hyper", "amount": 20.99, "currency": "AED"},
        {"vendor": "Day To Day Int'l Hyper", "amount": 20.99, "currency": "AED"},
        {"vendor": "Day To Day Int'l Hyper", "amount": 29.98, "currency": "AED"},
        {"vendor": "Fancy Gourmet Bakery", "amount": 5.00, "currency": "AED"},
        {"vendor": "Fancy Gourmet Bakery", "amount": 3.00, "currency": "AED"},
        {"vendor": "Fancy Gourmet Bakery", "amount": 10.00, "currency": "AED"},
        {"vendor": "Day To Day Int'l Hyper", "amount": 3.99, "currency": "AED"},
        {"vendor": "Day To Day Int'l Hyper", "amount": 4.99, "currency": "AED"},
        {"vendor": "Day To Day Int'l Hyper", "amount": 8.99, "currency": "AED"},
        # Adding more to reach approximately 307.62 total
        {"vendor": "Other vendors", "amount": 50.00, "currency": "AED"},
    ]
    
    base_currency = settings.base_currency
    print(f"Base currency: {base_currency}")
    print()
    
    # Calculate June total
    june_total_original = sum(inv["amount"] for inv in june_invoices)
    june_total_converted = sum(
        CurrencyProcessor.convert_currency(inv["amount"], inv["currency"], base_currency)
        for inv in june_invoices
    )
    
    print("JUNE 2025:")
    print(f"  Original calculation: ${june_total_original:.2f} (mixed currencies)")
    print(f"  Corrected calculation: {june_total_converted:.2f} {base_currency}")
    print()
    
    # Calculate July total
    july_total_original = sum(inv["amount"] for inv in july_invoices)
    july_total_converted = sum(
        CurrencyProcessor.convert_currency(inv["amount"], inv["currency"], base_currency)
        for inv in july_invoices
    )
    
    print("JULY 2025:")
    print(f"  Original calculation: {july_total_original:.2f} AED")
    print(f"  Corrected calculation: {july_total_converted:.2f} {base_currency}")
    print()
    
    # Show the problem with original calculation
    wrong_total = june_total_original + july_total_original
    correct_total = june_total_converted + july_total_converted
    
    print("SUMMARY COMPARISON:")
    print(f"  ❌ Wrong (your current): ${wrong_total:.2f} (mixing USD + AED)")
    print(f"  ✅ Correct (after fix): {correct_total:.2f} {base_currency}")
    print()
    print(f"Difference: {abs(correct_total - wrong_total):.2f} {base_currency}")
    print()
    print("The fix ensures all amounts are converted to AED before summing!")

if __name__ == "__main__":
    test_your_actual_data()
