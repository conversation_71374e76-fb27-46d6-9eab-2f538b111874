"""
AI Provider configurations and models.
"""

from typing import Dict, List, Optional, Any
from pydantic import BaseModel
from enum import Enum

class AIProvider(str, Enum):
    """Supported AI providers."""
    OPENAI = "openai"
    ANTHROPIC = "anthropic"
    GOOGLE = "google"
    DEEPSEEK = "deepseek"
    OPENROUTER = "openrouter"
    MISTRAL = "mistral"
    OLLAMA = "ollama"
    CUSTOM = "custom"

class ProviderConfig(BaseModel):
    """Configuration for an AI provider."""
    name: str
    base_url: str
    model: str
    vision_capable: bool = True
    max_tokens: int = 4000
    temperature: float = 0.1
    timeout: int = 60
    rate_limit_rpm: int = 60  # requests per minute
    supports_batch: bool = False
    cost_per_1k_tokens: float = 0.0

# Provider configurations
PROVIDER_CONFIGS: Dict[str, ProviderConfig] = {
    "openai": ProviderConfig(
        name="OpenAI GPT-4 Vision",
        base_url="https://api.openai.com/v1",
        model="gpt-4-vision-preview",
        max_tokens=4000,
        temperature=0.1,
        rate_limit_rpm=60,
        cost_per_1k_tokens=0.01
    ),
    
    "anthropic": ProviderConfig(
        name="Claude 3 Vision",
        base_url="https://api.anthropic.com",
        model="claude-3-sonnet-20240229",
        max_tokens=4000,
        temperature=0.1,
        rate_limit_rpm=50,
        cost_per_1k_tokens=0.003
    ),
    
    "google": ProviderConfig(
        name="Gemini Pro Vision",
        base_url="https://generativelanguage.googleapis.com/v1beta",
        model="gemini-pro-vision",
        max_tokens=4000,
        temperature=0.1,
        rate_limit_rpm=60,
        cost_per_1k_tokens=0.00025
    ),
    
    "deepseek": ProviderConfig(
        name="DeepSeek Chat + OCR",
        base_url="https://api.deepseek.com",
        model="deepseek-chat",
        vision_capable=True,  # Uses OCR + text processing
        max_tokens=4000,
        temperature=0.1,
        rate_limit_rpm=100,
        cost_per_1k_tokens=0.0002
    ),
    
    "openrouter": ProviderConfig(
        name="OpenRouter Multi-Model",
        base_url="https://openrouter.ai/api/v1",
        model="qwen/qwen-2-vl-7b-instruct:free",  # Free vision model
        max_tokens=4000,
        temperature=0.1,
        rate_limit_rpm=60,
        cost_per_1k_tokens=0.0  # Free model
    ),
    
    "mistral": ProviderConfig(
        name="Mistral Vision",
        base_url="https://api.mistral.ai/v1",
        model="pixtral-12b-2409",
        max_tokens=4000,
        temperature=0.1,
        rate_limit_rpm=60,
        cost_per_1k_tokens=0.0015
    ),
    
    "ollama": ProviderConfig(
        name="Ollama Local (DeepSeek-R1)",
        base_url="http://localhost:11434",
        model="deepseek-r1",
        vision_capable=False,  # Text-only model, will use OCR
        max_tokens=4000,
        temperature=0.1,
        rate_limit_rpm=1000,  # No rate limit for local
        cost_per_1k_tokens=0.0  # Free local processing
    ),
    
    "custom": ProviderConfig(
        name="Custom LLM",
        base_url="",  # Will be set from environment
        model="",     # Will be set from environment
        max_tokens=4000,
        temperature=0.1,
        rate_limit_rpm=60,
        cost_per_1k_tokens=0.0
    )
}

# Invoice processing prompts for different providers
INVOICE_EXTRACTION_PROMPTS = {
    "system": """You are an expert invoice data extraction assistant. Your task is to analyze invoice images and extract structured data with high accuracy.

IMPORTANT DATE INSTRUCTIONS:
- Current year is 2025
- Invoice dates should be realistic (typically within the last few months to current date)
- If you see dates like 2028, 2029, etc., these are likely OCR errors - correct them to 2025
- ALWAYS use DD/MM/YYYY format (Day/Month/Year) - NOT MM/DD/YYYY
- For example: July 8, 2025 should be written as 08/07/2025 (NOT 07/08/2025)
- Be very careful with date interpretation - double-check day vs month

Extract the following information from the invoice:
1. Date (MUST be in DD/MM/YYYY format - Day/Month/Year, ensure year is realistic - likely 2025)
2. Vendor/Store name
3. Product details (item descriptions)
4. Quantities
5. Unit prices
6. Total amount
7. Currency
8. Invoice/Receipt number (if available)
9. Tax amount (if available)
10. Payment method (if detectable)

Return the data in JSON format with the following structure:
{
    "date": "DD/MM/YYYY",
    "vendor_name": "Store Name",
    "products": [
        {
            "description": "Product description",
            "quantity": 1,
            "unit_price": 0.00,
            "total_price": 0.00
        }
    ],
    "total_amount": 0.00,
    "currency": "USD",
    "invoice_number": "INV-123",
    "tax_amount": 0.00,
    "payment_method": "Card/Cash/Digital",
    "confidence": 0.95,
    "language_detected": "en"
}

If any field cannot be determined, use null. Provide a confidence score (0-1) for the overall extraction quality.""",

    "user": "Please extract all the invoice data from this image. Be precise with numbers and dates. If text is unclear, indicate lower confidence."
}

def get_provider_config(provider: str) -> Optional[ProviderConfig]:
    """Get configuration for a specific provider."""
    return PROVIDER_CONFIGS.get(provider)

def get_extraction_prompt(provider: str) -> Dict[str, str]:
    """Get extraction prompt for a specific provider."""
    return INVOICE_EXTRACTION_PROMPTS
